<!--
  @description       : 
  <AUTHOR> Aryan C
  @group             : 
  @last modified on  : 07-14-2025
  @last modified by  : Aryan C
-->
<template>
    <!-- Modal Container -->
    <!-- <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" 
             class="slds-modal slds-fade-in-open slds-modal_large">
        <div class="slds-modal__container" style="width : 105% !important;">
            <hea class="slds-modal__header">
                <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" 
                        title="Close" onclick={closeModal}>
                    <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    <span class="slds-assistive-text">Close</span>
                </button>
                <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">
                    New Assessment Form
                </h2>
            </hea

             <div class="slds-modal__content slds-p-around_medium"> -->
                <lightning-quick-action-panel>


                                <template if:true={isLoading}>
                    <div class="slds-spinner_container">
                        <div role="status" class="slds-spinner slds-spinner_medium">
                            <span class="slds-assistive-text">Loading...</span>
                            <div class="slds-spinner__dot-a"></div>
                            <div class="slds-spinner__dot-b"></div>
                        </div>
                    </div>
                </template>
                <!-- Loading Spinner -->
                <!-- <div class="slds-modal__header">
                    <h2 class="slds-text-heading_medium slds-hyphenate">
                        New Assessment Form
                    </h2>
                </div> -->

                
                 <!-- <div class="slds-modal__content slds-p-around_medium"> -->

                <!-- Error Message -->
                <template if:true={errorMessage}>
                    <div class="slds-notify slds-notify_alert slds-theme_alert-texture slds-theme_error" role="alert">
                        <span class="slds-assistive-text">Error</span>
                        <h2>{errorMessage}</h2>
                    </div>
                </template>

                <!-- Main Content -->
                <template if:false={isLoading}>
                    <template if:false={errorMessage}>
                        <!-- Progress Indicator -->
                        <div class="slds-m-bottom_large">
                            <div class="slds-progress">
                                <ol class="slds-progress__list">
                                    <!-- <li class={step1Class}>
                                        <button class="slds-button slds-progress__marker" onclick={goToStep1}>
                                            <span class="slds-assistive-text">Step 1</span>
                                        </button>
                                       
                                    </li> -->
                                    <li class={step2Class}>
                                        <button class="slds-button slds-progress__marker" onclick={goToStep2}>
                                            <span class="slds-assistive-text">Step 2</span>
                                        </button>
                                       
                                    </li>
                                    <li class={step3Class}>
                                        <button class="slds-button slds-progress__marker" onclick={goToStep3}>
                                            <span class="slds-assistive-text">Step 3</span>
                                        </button>
                                      
                                    </li>
                                </ol>
                                <div class="slds-progress-bar slds-progress-bar_x-small" aria-valuemin="0" aria-valuemax="100" aria-valuenow={progressPercentage} role="progressbar">
                                    <span class="slds-progress-bar__value" style={progressBarStyle}>
                                        <span class="slds-assistive-text">Progress: {progressPercentage}%</span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Step 1: Claim Details -->
                        <!-- <template if:true={isStep1}>
                            <div class="slds-card">
                                <div class="slds-card__header slds-grid">
                                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                        <div class="slds-media__figure">
                                            <lightning-icon icon-name="standard:product_item" size="small"></lightning-icon>
                                        </div>
                                        <div class="slds-media__body">
                                            <h2 class="slds-card__header-title">
                                                <span>Claim Product Details</span>
                                            </h2>
                                        </div>
                                    </header>
                                </div>
                                <div class="slds-card__body slds-card__body_inner">
                                    <template if:true={claimProduct}>
                                        <lightning-layout multiple-rows="true">
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Item Name</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.prodName}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Issue Category</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.selectedIssueCategory}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Issue Sub Category</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.selectedIssueSubCategory}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Customer - Return Reason</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.assessmentFaultCategoryLable}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item if:true={isReturnReasonOther} size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Customer - Return Reason Description</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.assessmentFaultDescription}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Will a spare part fix the issue?</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.willSparePartCanFixIssue}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Batch Number</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.batchValue}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item if:true={hasBatchFileIds} size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <c-preview-file-thumbnails record-ids={batchFileIds} read-only=true></c-preview-file-thumbnails>
                                                </div>
                                            </lightning-layout-item>
                                           
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">SKU</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.sku}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Did the issue cause smoke, fire, electrocution, injury, property damage, heat damage or melting?</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.didCauseDamage}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                            <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                                <div class="slds-form-element">
                                                    <label class="slds-form-element__label">Shipping Status</label>
                                                    <div class="slds-form-element__control">
                                                        <span class="slds-form-element__static">{claimProduct.shippingMethod}</span>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                        </lightning-layout>
                                    </template>
                                </div>
                            </div>
                        </template> -->

                        <!-- Step 2: Assessment Details -->
                        <template if:true={isStep2}>
                            <div class="slds-card">
                                <!-- <div class="slds-card__header slds-grid">
                                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                        <div class="slds-media__figure">
                                            <lightning-icon icon-name="standard:form" size="small"></lightning-icon>
                                        </div>
                                        <div class="slds-media__body">
                                            <h2 class="slds-card__header-title">
                                                <span>Assessment Form Details</span>
                                            </h2>
                                        </div>
                                    </header>
                                </div> -->
                                <div class="slds-card__body slds-card__body_inner">
                                    <!-- Step 2 Validation Summary -->
                                    <!-- <div class={step2ValidationClass}>
                                        <div class="slds-text-heading_small slds-m-bottom_small">
                                            <lightning-icon icon-name={step2ValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            Step 2 Requirements
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={physicalConditionValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Physical Condition selected</span>
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={fridgeIssueValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Issue type selected</span>
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={modelValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Model selected</span>
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={picturesValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Pictures On File confirmed</span>
                                        </div>
                                    </div> -->

                                    <lightning-layout multiple-rows="true">
                                        <!-- Physical Condition -->
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <lightning-combobox
                                                data-groupname="step2"
                                                name="physicalCondition"
                                                label="Physical Condition"
                                                value={assessmentForm.Physical_Condition__c}
                                                placeholder="Select Physical Condition"
                                                options={physicalConditionOptions}
                                                onchange={handleFieldChange}
                                                data-field="Physical_Condition__c"
                                                disabled={disableForm}
                                                required>
                                            </lightning-combobox>
                                            <!-- <p class="slds-text-body_small slds-text-color_error slds-m-top_x-small">
                                                *This field is required
                                            </p> -->
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <div class="help-text-container">
                                                <!-- <h4 class="slds-text-heading_small">Physical Condition</h4> -->
                                                <p class="slds-text-body_small">
                                                Poor / Very bad condition = Scratched / missing parts / corroded terminals / covered in dirt/mud <br/>
                                                Used = Minor scratches / well used but no major damaged / showing signs of wear and tea.<br/>
                                                Good = Clean / no visible marks<br/>
                                                New = Boxed / new conditio</p>
                                            </div>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                             <lightning-combobox disabled={disableForm} data-groupname="step2" options={customerIssue} label="Customer Stated Issue" value={assessmentForm.Customer_Stated_Issue__c} onchange={handleFieldChange} data-field="Customer_Stated_Issue__c" required></lightning-combobox>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                           <lightning-input data-groupname="step2" step=".01" type="number" label="What is the voltage of the battery on arrival?" onchange={handleFieldChange} data-field="Voltage_on_Arrival__c"  value={assessmentForm.Voltage_on_Arrival__c} required disabled={disableForm}></lightning-input>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <div class="help-text-container">
                                                <!-- <h4 class="slds-text-heading_small">Issue</h4> -->
                                                <p class="slds-text-body_small">Photo with multimeter required on arrival required*</p>
                                            </div>
                                        </lightning-layout-item>

                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                           <lightning-combobox disabled={disableForm} data-groupname="step2" options={nowLaterOptions} label="Can the product be charged now?" value={assessmentForm.Can_the_test_be_started_now_or_later__c} onchange={handleFieldChange} data-field="Can_the_test_be_started_now_or_later__c" required></lightning-combobox>
                                        </lightning-layout-item>

                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <c-claim-form-file-upload if:false={disableForm} card-data={claimProduct} full-card-data={cardData} uploaded-files={claimProduct.uploadedFiles}
                                                onupdateddata={linkFilesToCard} 
                                                has-custom-field={trueVal}
                                                is-photo-only-upload={trueVal}
                                                custom-field-name="File_category_fileupload__c" custom-field-value="Photo with multimeter"
                                                input-label="Photo with multimeter" is-only-single-allowed={trueVal}>
                                            </c-claim-form-file-upload>
                                        </lightning-layout-item>

                                        <!-- <lightning-layout-item if:false={showTimerControls} size="12" large-device-size="6" padding="around-small">
                                        </lightning-layout-item> -->
                                        
                                        <template if:true={showTimerControls}>
                                        <lightning-layout-item size="12" large-device-size="6"  padding="around-small">
                                           <lightning-combobox disabled={disableForm} data-groupname="step2" options={yesNoOptions} label="Does the product require to be jump started?" value={assessmentForm.Jump_Started_Required__c} onchange={handleFieldChange} data-field="Jump_Started_Required__c" required></lightning-combobox>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <div class="help-text-container">
                                                <!-- <h4 class="slds-text-heading_small">Issue</h4> --> <button onclick={handleJumpStartVideo}>View Jumpstarter SOP</button>
                                                <p class="slds-text-body_small">Anything under 6V must be jumpstarted. Regardless of jumpstarting the product must put onto charger</p>
                                            </div>
                                            <template if:true={showJumpStart}>
                                                <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small"  aria-modal="true" >
                                                <div class="slds-modal__container">
                                                <header class="slds-modal__header">
                                                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={handleJumpStartVideo}>
                                                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                                                        <span class="slds-assistive-text">Close</span>
                                                    </button>
                                                    <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">Jumpstarter SOP</h2>
                                                </header>
                                                <div class="slds-modal__content slds-p-around_medium" >
                                                   <lightning-formatted-rich-text value={article.Details__c}></lightning-formatted-rich-text>
                                                </div>
                                                </div>
                                                </section>
                                                <div class="slds-backdrop slds-backdrop_open"></div>
                                            </template>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <!-- <div class="slds-m-bottom_small">
                                                <div class="slds-grid slds-grid_align-start slds-wrap">
                                                    <div class="slds-col slds-m-right_small">
                                                        <lightning-button
                                                            variant="brand"
                                                            label="Start"
                                                            onclick={handleStartTimer}
                                                            disabled={isTimerRunning}
                                                            icon-name="utility:play"
                                                            size="small">
                                                        </lightning-button>
                                                    </div>
                                                    <div class="slds-col slds-m-right_small">
                                                        <lightning-button
                                                            variant="destructive"
                                                            label="Stop"
                                                            onclick={handleStopTimer}
                                                            disabled={stopButtonDisabled}
                                                            icon-name="utility:stop"
                                                            size="small">
                                                        </lightning-button>
                                                    </div>
                                                    <div class="slds-col">
                                                        <template if:true={shouldShowTimer}>
                                                            <div class="timer-display-compact">
                                                                <span class={timerTextClass}>{timerDisplay}</span>
                                                                <p class="slds-text-body_small slds-text-color_weak timer-status">{timerStatusText}</p>
                                                            </div>
                                                        </template>
                                                    </div>
                                                </div>
                                            </div> -->

                                        <lightning-input disabled={disableForm} data-groupname="step2" type="datetime-local" label="AC/DC Charge Start Time/Date" value={assessmentForm.AC_DC_Charge_Start_Time_Date__c} data-field="AC_DC_Charge_Start_Time_Date__c" onchange={handleDateTimeChange} required></lightning-input>
                                        </lightning-layout-item>

                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <div class="help-text-container">
                                                <!-- <h4 class="slds-text-heading_small">Issue</h4> -->
                                                 <p class="slds-text-body_small">it is always recommended to charge unless there is a heat related issue.
                                                This forms the 'start time of test' - Time * back end, not visible
                                                Captured as 'Waiting to be tested' - Time * back end, not visibile
                                                </p>
                                            </div>
                                        </lightning-layout-item>

                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <lightning-layout>
                                           <lightning-input disabled={disableForm} data-groupname="step2" type="datetime-local" label="AC/DC Charge Finish Time/Date" value={assessmentForm.AC_DC_Charge_Finish_Time_Date__c} data-field="AC_DC_Charge_Finish_Time_Date__c" onchange={handleDateTimeChange} ></lightning-input>
                                           </lightning-layout>
                                            <lightning-layout>
                                                <lightning-button
                                                    label="Set Current Time"
                                                    onclick={handleStopTimer}
                                                    size="small" disabled={disableForm}>
                                                </lightning-button>
                                            </lightning-layout>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <div class="help-text-container">
                                                <!-- <h4 class="slds-text-heading_small">Issue</h4> -->
                                                 <p class="slds-text-body_small">Must capture start video of product on test (this will show us the current 'lights' and ensure the product is connected correctly).
                                                </p>
                                            </div>
                                        </lightning-layout-item>
                                        

                                         <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                           <lightning-combobox disabled={disableForm} data-groupname="step2" options={yesNoChargingOptions} label="Did the product charge correctly?" value={assessmentForm.Did_the_product_charge_correctly__c} onchange={handleFieldChange} data-field="Did_the_product_charge_correctly__c" required={assessmentForm.AC_DC_Charge_Finish_Time_Date__c} ></lightning-combobox>
                                        </lightning-layout-item>
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <div class="help-text-container">
                                                <!-- <h4 class="slds-text-heading_small">Issue</h4> -->
                                                 <p class="slds-text-body_small">If the battery has failed to charge, a short video showing the battery connected to the charger & the charger status must be uploaded
                                                </p>
                                            </div>
                                        </lightning-layout-item>
                                          
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                           <lightning-input disabled={disableForm} data-groupname="step2" step=".01" type="number" label="Voltage after charging" value={assessmentForm.Voltage_after_charging__c} data-field="Voltage_after_charging__c" onchange={handleFieldChange}></lightning-input>
                                        </lightning-layout-item>

                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                           
                                        </lightning-layout-item>
                                        
                                        </template>
                                        <template if:true={isFilesUploaded}>
                                            <lightning-layout-item class="slds-p-right_small slds-p-bottom_small" size="12" large-device-size="12"
                                                flexibility="auto" multiple-rows>
                                                <div class="slds-form-element">
                                                    <span class="slds-form-element__label slds-text-title_bold" style="color: black;">
                                                        
                                                        Uploaded Files
                                                    </span>
                                                    <div class="slds-form-element__control">
                                                        <c-preview-file-thumbnails read-only={approvalScreen} record-ids={fileIds}
                                                            onremovefile={removeFileForProductIssue}></c-preview-file-thumbnails>
                                                    </div>
                                                </div>
                                            </lightning-layout-item>
                                        </template>
                                    </lightning-layout>
                                </div>
                            </div>
                        </template>

                        <!-- Step 3: Product Testing -->
                        <template if:true={isStep3}>
                            <div class="slds-card">
                                <!-- <div class="slds-card__header slds-grid">
                                    <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                        <div class="slds-media__figure">
                                            <lightning-icon icon-name="standard:product_consumed" size="small"></lightning-icon>
                                        </div>
                                        <div class="slds-media__body">
                                            <h2 class="slds-card__header-title">
                                                <span>Product Testing - Stage 2</span>
                                            </h2>
                                        </div>
                                    </header>
                                </div> -->
                                <div class="slds-card__body slds-card__body_inner">
                                    <!-- Step 3 Validation Summary -->
                                    <!-- <div class={step3ValidationClass}>
                                        <div class="slds-text-heading_small slds-m-bottom_small">
                                            <lightning-icon icon-name={step3ValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            Step 3 Requirements
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={startTimeValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Load Test Start Time/Date</span>
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={finishTimeValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Load Test Finish Time/Date</span>
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={testedCapacityValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Tested Capacity (AH)</span>
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={assessmentOutcomeValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Assessment Outcome</span>
                                        </div>
                                        <div class="validation-item">
                                            <lightning-icon icon-name={testingInfoValidationIcon} size="x-small" class="validation-icon"></lightning-icon>
                                            <span>Testing Information Provided</span>
                                        </div>
                                    </div> -->

                                    <!-- Battery Capacity Table -->
                                    <div if:true={capacityTableInfo} class="slds-m-bottom_large">
                                        <c-battery-capacity-table-html 
                                            capacity-info={capacityTableInfo}
                                            title="Battery Capacity Specifications"
                                            show-border="true"
                                            show-striped="true">
                                        </c-battery-capacity-table-html>
                                    </div>

                                    <!-- Testing Information Note -->
                                    <div if:true={capacityTableInfo} class="slds-box slds-theme_shade slds-m-bottom_medium">
                                        <p class="slds-text-body_small">
                                            <strong>Note:</strong> If the battery capacity is greater than 90% of the advertised rate, the battery has passed capacity testing.
                                        </p>
                                    </div>

                                    <!-- Testing Fields -->
                                    <lightning-layout multiple-rows="true">
                                        <!-- Load Test Start Time/Date -->
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <lightning-input
                                                data-groupname="step3"
                                                type="datetime-local"
                                                label="Load Test Start Time/Date"
                                                value={assessmentForm.Load_Test_Start_Time_Date__c}
                                                data-field="Load_Test_Start_Time_Date__c"
                                                onchange={handleDateTimeChange}
                                                required
                                                disabled={disableForm}
                                                class="slds-m-bottom_small">
                                            </lightning-input>
                                            <p class="slds-text-body_small slds-text-color_weak">
                                                Calendar = today - cannot back date<br/>
                                                Time = Staff Input
                                            </p>
                                        </lightning-layout-item>

                                        <!-- Load Test Finish Time/Date -->
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <lightning-input
                                                data-groupname="step3"
                                                type="datetime-local"
                                                label="Load Test Finish Time/Date"
                                                value={assessmentForm.Load_Test_Finish_Time_Date__c}
                                                data-field="Load_Test_Finish_Time_Date__c"
                                                onchange={handleDateTimeChange}
                                                required
                                                disabled={disableForm}
                                                class="slds-m-bottom_small">
                                            </lightning-input>
                                            <p class="slds-text-body_small slds-text-color_weak">
                                                Calendar = today - cannot back date<br/>
                                                Time = Staff Input
                                            </p>
                                        </lightning-layout-item>

                                        <!-- Tested Capacity -->
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <lightning-input
                                                type="text"
                                                data-groupname="step3"
                                                label="Tested Capacity (AH)"
                                                value={assessmentForm.Tested_Capacity_AH__c}
                                                data-field="Tested_Capacity_AH__c"
                                                onchange={handleFieldChange}
                                                required
                                                disabled={disableForm}
                                                placeholder="Input level">
                                            </lightning-input>
                                        </lightning-layout-item>

                                        <!-- Assessment Outcome -->
                                        <lightning-layout-item size="12" large-device-size="6" padding="around-small">
                                            <lightning-combobox
                                                label="Assessment Outcome"
                                                data-groupname="step3"
                                                value={finalOutcome}
                                                options={assessmentOutcomeOptions}
                                                data-field="Assessment_Outcome__c"
                                                onchange={handleFieldChange}
                                                disabled={disableOutcome}
                                                required>
                                            </lightning-combobox>
                                        </lightning-layout-item>

                                    </lightning-layout>

                                    <!-- Additional Requirements -->
                                    <div class="slds-m-top_medium">

                                        <div class="slds-box slds-theme_info slds-m-bottom_small">
                                            <p class="slds-text-body_small">
                                                Must capture start video of product on test (this will show us that the AH draw is not flashing).
                                            </p>
                                        </div>

                                        <div class="slds-box slds-theme_info slds-m-bottom_small">
                                            <p class="slds-text-body_small">
                                                As the load tester will turn off at the end of the test. All submissions require a video showing the 'On' button being pressed and the test finalising.
                                            </p>
                                        </div>

                                        <div class="slds-box slds-theme_shade slds-m-bottom_small">
                                            <p class="slds-text-body_small">
                                                <strong>NOTE:</strong> Fully recharge any battery that has passed and return it to the customer.
                                            </p>
                                        </div>

                                        <div class="slds-m-top_medium">
                                            <lightning-input
                                                disabled={disableForm}
                                                type="checkbox"
                                                data-groupname="step3" 
                                                data-field="providedAllInfo" 
                                                label="I have provided all required information to show the battery has passed or failed the testing. This includes the Batch Number / Serial Number (if available) and ALL videos / photos requested during each stage."
                                                checked={assessmentForm.providedAllInfo}
                                                onchange={handleFieldChange}
                                                required>
                                            </lightning-input>
                                            <!-- <p class="slds-text-body_small slds-text-color_error slds-m-top_x-small">
                                                *This field is mandatory for assessment completion
                                            </p> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </template>
                </template>
                <!-- </div> -->
            <!-- </div>

            <footer class="slds-modal__footer">
                <button class="slds-button slds-button_neutral" onclick={closeModal}>Cancel</button>
                <button class="slds-button slds-button_neutral" 
                        onclick={handlePrevious} 
                        disabled={isPreviousDisabled}>
                    Previous
                </button>
                <button class="slds-button slds-button_brand" 
                        onclick={handleNext} 
                        disabled={isNextDisabled}>
                    {nextButtonLabel}
                </button>
            </footer>
        </div>
    </section> -->
    <!-- <div class="slds-backdrop slds-backdrop_open"></div> -->
        <!-- <div class="slds-modal__header"> -->
                <div slot="footer">
            <!-- <button class="slds-button slds-button_neutral" onclick={closeModal} disabled={disableForm}>Clear All</button> -->
                <button class="slds-button slds-button_neutral" 
                        onclick={handlePrevious} 
                        disabled={isPreviousDisabled}>
                    Previous
                </button>
                <!-- <button if:true={isStep2} class="slds-button slds-button_brand" 
                        onclick={handleNext} name="Save As Draft">
                    Save As Draft
                </button> -->
                <button if:false={isStep3} class="slds-button slds-button_brand" 
                        onclick={handleNext} 
                        disabled={isNextDisabled}>
                    {nextButtonLabel}
                </button>
            <!-- </div> -->
                 </div>
  </lightning-quick-action-panel>
</template>
