/*
    @File Name          : claimFormOrderItems.js
    <AUTHOR> AFDigital
    @Last Modified By   : AFDigital
    @Last Modified On   : 07-14-2025
    @Modification Log   :
    ==============================================================================
        Ver         Date             Author      	Modification
    ==============================================================================
        1.0      10/29/2021         AFDigital       Initial Version
        1.1      02/22/2022         AFDigital       [CLA-239]: Trim Billing and Shipping Address Field Value
        1.2      03/08/2022         AFDigital       [CLA-244]
        1.3      04/01/2022         AFDigital       [CLA-254]
        1.4      04/18/2022         AFDigital       [CLA-73]
        1.5      06/14/2022         AFDigital       [CLA-299]
        1.6      10/09/2023         AFDigital       [SF-60]
*/

import { LightningElement, api, wire, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

// ACCOUNT FIELDS
import ACCOUNT_ID_FIELD from '@salesforce/schema/Account.Id';                                                               // import Account - Id Field Schema
import ACCOUNT_FIRST_NAME_FIELD from '@salesforce/schema/Account.FirstName';                                                // import Account - First Name Field Schema
import ACCOUNT_LAST_NAME_FIELD from '@salesforce/schema/Account.LastName';                                                  // import Account - Last Name Field Schema
import ACCOUNT_EMAIL_FIELD from '@salesforce/schema/Account.PersonEmail';                                                   // import Account - Person Email Field Schema
import ACCOUNT_CUSTOM_EMAIL_FIELD from '@salesforce/schema/Account.Email__c';                                               // import Account - Email (Custom) Field Schema
import ACCOUNT_PHONE_FIELD from '@salesforce/schema/Account.Phone';                                                         // import Account - Phone Field Schema

// CASE FIELDS
import CASE_ACCOUNT_ID_FIELD from '@salesforce/schema/Case.AccountId';                                                      // import Case - Account Id Field Schema
import CASE_CONTACT_ID_FIELD from '@salesforce/schema/Case.ContactId';                                                      // import Case - Contact Id Field Schema
import CASE_ORDER_ID_FIELD from '@salesforce/schema/Case.Order_Id__c';                                                      // import Case - Order Id Field Schema
import CASE_CASE_OWNER_FIELD from '@salesforce/schema/Case.OwnerId';                                                        // import Case - Owner Id Field Schema
import CASE_SUBJECT_FIELD from '@salesforce/schema/Case.Subject';                                                           // import Case - Subject Field Schema
import CASE_CASE_ORIGIN_FIELD from '@salesforce/schema/Case.Origin';                                                        // import Case - Origin Field Schema
import CASE_TYPE_FIELD from '@salesforce/schema/Case.Type';                                                                 // import Case - Type Field Schema
import CASE_TURN_OFF_CUSTOMER_COMMUNICATIONS_FIELD from '@salesforce/schema/Case.Turn_Off_Customer_Communications__c';      // import Case - Type Field Schema
//import CASE_REASON_FIELD from '@salesforce/schema/Case.Reason__c';                                                          // import Case - Turn Off Customer Communications Field Schema
import CASE_DESCRIPTION_FIELD from '@salesforce/schema/Case.Description';                                                   // import Case - Description Field Schema
import CASE_SEND_NOTIFICATION_DATETIME_FIELD from '@salesforce/schema/Case.Send_Notification_DateTime__c';                  // import Case - Send Notification DateTime Field Schema
import CASE_END_USER_ADDRESS_FIELD from '@salesforce/schema/Case.End_User_Address__c';                                      // import Case - End User Address Field Schema
import CASE_END_USER_EMAIL_FIELD from '@salesforce/schema/Case.End_User_Email__c';                                          // import Case - End User Email Field Schema
import CASE_END_USER_MOBILE_FIELD from '@salesforce/schema/Case.End_User_Mobile__c';                                        // import Case - End User Mobile Field Schema
import CASE_END_USER_NAME_FIELD from '@salesforce/schema/Case.End_User_Name__c';                                            // import Case - End User Name Field Schema
import CASE_END_USER_SUPPLIED_REPLACEMENT_FIELD from '@salesforce/schema/Case.End_User_Supplied_Replacement__c';            // import Case - End User Supplied Replacement Field Schema
import CASE_IS_CLONED_FIELD from '@salesforce/schema/Case.Is_Cloned__c';                                                    // import Case - Is Cloned Field Schema
import CASE_PARENT_ID_FIELD from '@salesforce/schema/Case.ParentId';  
import CASE_OBJECT from '@salesforce/schema/Case';                                                     // import Case - Parent ID Field Schema

// ORDER FIELDS
import ORDER_ID_FIELD from '@salesforce/schema/Order.Id';                                                                   // import Order - Id Field Schema
import ORDER_ORDER_NUMBER_FIELD from '@salesforce/schema/Order.Order_Number__c';                                            // import Order - Order Number Field Schema
import ORDER_ACCOUNT_ID_FIELD from '@salesforce/schema/Order.AccountId';                                                    // import Order - Account Id Field Schema
import ORDER_ORDER_TYPE_FIELD from '@salesforce/schema/Order.Type';                                                         // import Order - Order Type Field Schema
import ORDER_CASE_ID_FIELD from '@salesforce/schema/Order.Case_Id__c';                                                      // import Order - Case Id Field Schema
import ORDER_STORE_NAME_FIELD from '@salesforce/schema/Order.Store_Name__c';                                                // import Order - Store Name Field Schema
import ORDER_ORDER_START_DATE_FIELD from '@salesforce/schema/Order.EffectiveDate';                                          // import Order - Effective Date Field Schema
import ORDER_STATUS_FIELD from '@salesforce/schema/Order.Status';                                                           // import Order - Status Field Schema
import ORDER_SHIPPING_FIRST_NAME_FIELD from '@salesforce/schema/Order.Shipping_First_Name__c';                              // import Order - Shipping First Name Field Schema
import ORDER_SHIPPING_LAST_NAME_FIELD from '@salesforce/schema/Order.Shipping_Last_Name__c';                                // import Order - Shipping Last Name Field Schema
import ORDER_SHIPPING_STATE_FIELD from '@salesforce/schema/Order.ShippingState';                                            // import Order - Shipping State Schema
import ORDER_SHIPPING_POSTAL_CODE_FIELD from '@salesforce/schema/Order.ShippingPostalCode';                                 // import Order - Shipping Postal Code Field Schema
import ORDER_SHIPPING_STREET_FIELD from '@salesforce/schema/Order.ShippingStreet';                                          // import Order - Shipping Street Field Schema
import ORDER_SHIPPING_CITY_FIELD from '@salesforce/schema/Order.ShippingCity';                                              // import Order - Shipping City Field Schema
import ORDER_API_SHIPPING_CITY_FIELD from '@salesforce/schema/Order.API_Shipping_City__c';                                  // import Order - API Shipping City Field Schema
import ORDER_SHIPPING_COUNTRY_FIELD from '@salesforce/schema/Order.ShippingCountry';                                        // import Order - Shipping Country Field Schema
import ORDER_SHIPPING_EMAIL_FIELD from '@salesforce/schema/Order.Shipping_Email__c';                                        // import Order - Shipping Email Field Schema
import ORDER_SHIPPING_TELEPHONE_FIELD from '@salesforce/schema/Order.Shipping_Telephone__c';                                // import Order - Shipping Telephone Field Schema
import ORDER_BILLING_FIRST_NAME_FIELD from '@salesforce/schema/Order.Billing_First_Name__c';                                // import Order - Billing First Name Field Schema
import ORDER_BILLING_LAST_NAME_FIELD from '@salesforce/schema/Order.Billing_Last_Name__c';                                  // import Order - Billing Last Name Field Schema
import ORDER_BILLING_STATE_FIELD from '@salesforce/schema/Order.BillingState';                                              // import Order - Billing State Field Schema
import ORDER_BILLING_POSTAL_CODE_FIELD from '@salesforce/schema/Order.BillingPostalCode';                                   // import Order - Billing Postal Code Field Schema
import ORDER_BILLING_STREET_FIELD from '@salesforce/schema/Order.BillingStreet';                                            // import Order - Billing Street Field Schema
import ORDER_BILLING_CITY_FIELD from '@salesforce/schema/Order.BillingCity';                                                // import Order - Billing City Field Schema
import ORDER_API_BILLING_CITY_FIELD from '@salesforce/schema/Order.API_Billing_City__c';                                    // import Order - API Billing City Field Schema
import ORDER_BILLING_COUNTRY_FIELD from '@salesforce/schema/Order.BillingCountry';                                          // import Order - Billing Country Field Schema
import ORDER_BILLING_EMAIL_FIELD from '@salesforce/schema/Order.Billing_Email__c';                                          // import Order - Billing Email Field Schema
import ORDER_BILLING_TELEPHONE_FIELD from '@salesforce/schema/Order.Billing_Telephone__c';                                  // import Order - Billing Telephone Field Schema
import ORDER_ORDER_VALUE_FIELD from '@salesforce/schema/Order.Order_Value__c';                                              // import Order - Order Value Field Schema
import ORDER_SALES_CHANNEL_FIELD from '@salesforce/schema/Order.Sales_Channel__c';                                          // import Order - Sales Channel Field Schema
import ORDER_REFUND_TYPE_FIELD from '@salesforce/schema/Order.Refund_Type__c';                                              // import Order - Refund Type Field Schema
import ORDER_PAYMENT_METHOD_FIELD from '@salesforce/schema/Order.Payment_Method__c';                                        // import Order - Payment Method Field Schema
import ORDER_PAYMENT_REFERENCE_FIELD from '@salesforce/schema/Order.Payment_Reference__c';                                  // import Order - Payment Reference Field Schema
import ORDER_SHIPPING_AMOUNT_FIELD from '@salesforce/schema/Order.Shipping_Amount__c';                                      // import Order - Shipping Amount Field Schema
import ORDER_DESCRIPTION_FIELD from '@salesforce/schema/Order.Description';                                                 // import Order - Description Field Schema

// ORDER PRODUCT FIELDS
import ORDER_PRODUCT_ID_FIELD from '@salesforce/schema/OrderItem.Id';                                                       // improt Order Product - Id Field Schema
import ORDER_PRODUCT_ITEM_ID_FIELD from '@salesforce/schema/OrderItem.Item_Id__c';                                          // import Order Product - Item Id Field Schema
import ORDER_PRODUCT_PRODUCT_ID_FIELD from '@salesforce/schema/OrderItem.Product2Id';                                       // import Order Product - Product Id Field Schema
import ORDER_PRODUCT_CASE_ID_FIELD from '@salesforce/schema/OrderItem.Case_Id__c';                                          // import Order Product - Case Id Field Schema
import ORDER_PRODUCT_DESCRIPTION_FIELD from '@salesforce/schema/OrderItem.Description';                                     // import Order Product - Description Field Schema
import ORDER_PRODUCT_ORDER_ID_FIELD from '@salesforce/schema/OrderItem.OrderId';                                            // import Order Product - Order Id Field Schema
import ORDER_PRODUCT_QUANTITY_FIELD from '@salesforce/schema/OrderItem.Quantity';                                           // import Order Product - Quantity Field Schema
import ORDER_PRODUCT_AVAILABLE_QUANTITY_TO_CLAIM_FIELD from '@salesforce/schema/OrderItem.Available_Quantity_to_Claim__c';  // import Order Product - Available Quantity to Claim Field Schema
import ORDER_PRODUCT_QUANTITY_SHIPPED_FIELD from '@salesforce/schema/OrderItem.Quantity_Shipped__c';                        // import Order Product - Quantity Shipped Field Schema
import ORDER_PRODUCT_UNIT_PRICE_FIELD from '@salesforce/schema/OrderItem.UnitPrice';                                        // import Order Product - Unit Price Field Schema
import ORDER_PRODUCT_ROW_TOTAL_PRICE_FIELD from '@salesforce/schema/OrderItem.Row_Total_Price__c';                          // import Order Product - Row Total Price Field Schema
import ORDER_PRODUCT_COST_FIELD from '@salesforce/schema/OrderItem.Cost__c';                                                // import Order Product - Cost Field Schema
import ORDER_PRODUCT_SHIPPING_REFERENCE_FIELD from '@salesforce/schema/OrderItem.Shipping_Reference__c';                    // import Order Product - Shipping Reference Field Schema
import ORDER_PRODUCT_JSON_ID_FIELD  from '@salesforce/schema/OrderItem.JSON_ID__c';                                         // import Order Product - JSON ID Field Schema

// CLAIM PRODUCT FIELDS
import CLAIM_PRODUCT_CASE_ID_FIELD from '@salesforce/schema/Claim_Product__c.Case_Id__c';                                   // import Claim Product - Case Id Field Schema
import CLAIM_PRODUCT_ITEM_ID_FIELD from '@salesforce/schema/Claim_Product__c.Item_Id__c';                                   // import Claim Product - Case Item Id Field Schema
import CLAIM_PRODUCT_ORDER_PRODUCT_ID_FIELD from '@salesforce/schema/Claim_Product__c.Order_Product_Id__c';                 // import Claim Product - Order Product Id Field Schema
import CLAIM_PRODUCT_PRODUCT_ID_FIELD from '@salesforce/schema/Claim_Product__c.Product_Id__c';                             // import Claim Product - Product Id Field Schema
import CLAIM_PRODUCT_BATCH_NUMBER_FIELD from '@salesforce/schema/Claim_Product__c.Batch_Number__c';                         // import Claim Product - Product Id Field Schema
import CLAIM_PRODUCT_SERIAL_NUMBER_FIELD from '@salesforce/schema/Claim_Product__c.Serial_Number__c';                       // import Claim Product - Serial Number Field Schema
import RETURN_ORDER_TRACKING_NUMBER_FIELD from '@salesforce/schema/Claim_Product__c.Return_Order_Tracking_Number__c'         // import Claim Product - Return Order Tracking Number
import CLAIM_PRODUCT_RESOLUTION_FIELD from '@salesforce/schema/Claim_Product__c.Resolution__c';                             // import Claim Product - Resolution Field Schema
import CLAIM_PRODUCT_IS_CHILD_ITEM_FIELD from '@salesforce/schema/Claim_Product__c.Is_Child_Item__c';                       // import Claim Product - Is Child Item Field Schema
import CLAIM_PRODUCT_UPLOADED_FILES_ID_FIELD from '@salesforce/schema/Claim_Product__c.Uploaded_Files_ID__c';               // import Claim Product - Uploaded Files ID Field Schema
import CLAIM_PRODUCT_ITEM_COST_FIELD from '@salesforce/schema/Claim_Product__c.Item_Cost_Currency__c';                      // import Claim Product - Item Cost Field Schema
import CLAIM_PRODUCT_REASON_FIELD from '@salesforce/schema/Claim_Product__c.Reason__c';                                     // import Claim Product - Reason Field Schema
import CLAIM_PRODUCT_ACL_SEVERITY_FIELD from '@salesforce/schema/Claim_Product__c.ACL_Severity__c';                              // import Claim Product - ACL Seeverity Field Schema
import CLAIM_PRODUCT_NEEDS_ASSESMENT_FIELD from '@salesforce/schema/Claim_Product__c.Needs_Assessment__c';                       // import Claim Product - Needs Assessment? Field Schema
import CLAIM_PRODUCT_RETURNED_FIELD from '@salesforce/schema/Claim_Product__c.Returned__c';                                      // import Claim Product - Returned? Field Schema
import CLAIM_PRODUCT_RETURNED_LOCATION_FIELD from '@salesforce/schema/Claim_Product__c.Returned_Location__c';                    // import Claim Product - Returned Location Field Schema
import CLAIM_PRODUCT_RETURNED_DATE_TIME_FIELD from '@salesforce/schema/Claim_Product__c.Returned_Datetime__c';                   // import Claim Product - Returned DateTime Field Schema
import CLAIM_PRODUCT_RETURNED_STAFF_MEMBER_NAME_FIELD from '@salesforce/schema/Claim_Product__c.Returned_Staff_Member_Name__c';  // import Claim Product - Returned Staff Member Name Field Schema
import CLAIM_PRODUCT_MUST_RETURN_FIELD from '@salesforce/schema/Claim_Product__c.Must_Return__c';                                // import Claim Product - Must Return Field Schema
import CLAIM_PRODUCT_RECEIVED_AT_DC_FIELD from '@salesforce/schema/Claim_Product__c.Received_at_DC__c';                          // import Claim Product - Received at DC Field Schema
import CLAIM_PRODUCT_DC_OUTCOME_FIELD from '@salesforce/schema/Claim_Product__c.DC_Outcome__c';                                  // import Claim Product - DC Outcome Field Schema
import CLAIM_PRODUCT_DC_OUTCOME_DATETIME_FIELD from '@salesforce/schema/Claim_Product__c.CP_Final_Outcome_Datetime__c';          // import Claim Product - DC Outcome DateTime Field Schema
import CLAIM_PRODUCT_STATUS_FIELD from '@salesforce/schema/Claim_Product__c.Status__c';                                          // import Claim Product - Status Field Schema
import CLAIM_PRODUCT_ADD_BACK_TO_STOCK_FIELD from '@salesforce/schema/Claim_Product__c.Add_Back_to_Stock__c';                    // import Claim Product - Add Back to Stock Field Schema
import CLAIM_PRODUCT_ITEM_PRICE_FIELD from '@salesforce/schema/Claim_Product__c.Item_Price_Currency__c';                         // import Claim Product - Item Price Field Schema
import CLAIM_PRODUCT_ADVERTISED_PRICE_FIELD from '@salesforce/schema/Claim_Product__c.Advertised_Price__c';                      // import Claim Product - Advertised Price Field Schema
import CLAIM_PRODUCT_CLAIM_FORM_ID_FIELD from '@salesforce/schema/Claim_Product__c.Claim_Form_ID__c';                            // import Claim Product - Claim Form ID Field Schema
import CLAIM_PRODUCT_APPROVED_BY_FIELD from '@salesforce/schema/Claim_Product__c.Approved_By__c';                                // import Claim Product - Approved By Field Schema
import CLAIM_PRODUCT_APPROVED_BY_PIN_NAME_FIELD from '@salesforce/schema/Claim_Product__c.Approved_By_Pin_Name__c';              // import Claim Product - Approved By Pin Name Field Schema
import CLAIM_PRODUCT_CP_ITEM_LOCATION from '@salesforce/schema/Claim_Product__c.CP_Item_Location__c';                            // import Claim Product - CP Item Location Field Schema
import CLAIM_PRODUCT_RECEIVED_AT_STORE_DATETIME from '@salesforce/schema/Claim_Product__c.Received_at_Store_Datetime__c';        // import Claim Product - Received at store Datetime Field Schema
import CLAIM_PRODUCT_TRANSIT_TO_DC_DATETIME from '@salesforce/schema/Claim_Product__c.Transit_to_DC_Datetime__c';                // import Claim Product - Received at store Datetime Field Schema
import CLAIM_PRODUCT_RETURNED_TO_DC_DATETIME from '@salesforce/schema/Claim_Product__c.Returned_DC_Datetime__c';                 // import Claim Product - Received at store Datetime Field Schema
import CLAIM_PRODUCT_RESOLUTION_ACTION_FIELD from '@salesforce/schema/Claim_Product__c.Resolution_Action__c';                    // import Claim Product - Resolution Action Field Schema
import CLAIM_PRODUCT_FREIGHT_PAID_FIELD from '@salesforce/schema/Claim_Product__c.Freight_Paid__c';                              // import Claim Product - Freight Paid Field Schema
import CLAIM_PRODUCT_FREIGHT_REFUNDED_FIELD from '@salesforce/schema/Claim_Product__c.Freight_Refunded__c';                      // import Claim Product - Freight Refunded Field Schema



import CLAIM_PRODUCT_APPROVAL_STATUS_FIELD from '@salesforce/schema/Claim_Product__c.Approval_Status__c';                        // import Claim Product - Approval Status Field Schema
import CLAIM_PRODUCT_DID_THIS_FAULTY_PRODUCT_CAUSE_DAMAGE_FIELD from '@salesforce/schema/Claim_Product__c.Did_this_Faulty_Product_cause_Damage__c'; // import Claim Product - Did this Faulty Product cause Damage? Field Schema
import CLAIM_PRODUCT_SHIPPING_STATUS_FIELD from '@salesforce/schema/Claim_Product__c.Shipping_Status__c';                        // import Claim Product - Shipping Status Field Schema
import CLAIM_PRODUCT_SHIPPING_TO_ADDRESS_FIELD from '@salesforce/schema/Claim_Product__c.Shipping_To_Address__c';                // import Claim Product - Shipping To Address Field Schema
import CLAIM_PRODUCT_ASSESSMENT_STATUS_FIELD from '@salesforce/schema/Claim_Product__c.Assessment_Status__c';                     // import Claim Product - Assessment Status Field Schema
import CLAIM_PRODUCT_ASSESSMENT_FAULT_DESCRIPTION_FIELD from '@salesforce/schema/Claim_Product__c.Assesment_Fault_Description__c';   // import Claim Product - Assessment Fault Description Field Schema
import CLAIM_PRODUCT_ASSESSMENT_OUTCOME_FIELD from '@salesforce/schema/Claim_Product__c.Assessment_Outcome__c';                   // import Claim Product - Assessment Outcome Field Schema
import CLAIM_PRODUCT_ASSESSMENT_STAFF_NAME_FIELD from '@salesforce/schema/Claim_Product__c.Assessment_Staff_Name__c';             // import Claim Product - Assessment Staff Name Field Schema
import CLAIM_PRODUCT_ASSESSMENT_FAULT_CATEGORY from '@salesforce/schema/Claim_Product__c.Assessment_Fault_Category__c'            // import Claim Product - Assessment Fault Category Field Schema
import CLAIM_PRODUCT_ADD_BACK_TO_STOCK_LOCATION_FIELD from '@salesforce/schema/Claim_Product__c.Add_Back_to_Stock_Location__c';   // import Claim Product - Add Back to Stock Location Field Schema
import CLAIM_PRODUCT_RETURN_METHOD_FIELD from '@salesforce/schema/Claim_Product__c.Return_Method__c';                             // import Claim Product - Return Method Field Schema
import CLAIM_PRODUCT_RETURN_CONNOTE_NUMBER_FIELD from '@salesforce/schema/Claim_Product__c.Return_Connote_Number__c';             // import Claim Product - Return Connote Number Field Schema
import CLAIM_PRODUCT_RETURN_COURIER_NAME_FIELD from '@salesforce/schema/Claim_Product__c.Courier__c';             // import Claim Product -  Courier Name Field Schema
import CLAIM_PRODUCT_REASON_SUB_CATEGORY_FIELD from '@salesforce/schema/Claim_Product__c.Reason_Sub_Category__c';                 // import Claim Product - Reason Sub-Category Field Schema
import WHY_HAS_THE_PRODUCT_BEEN_RETURNED_FIELD from '@salesforce/schema/Claim_Product__c.Why_has_the_product_been_returned__c';   // import Claim Product - Why has the product been returned?
import PRODUCT_RTS_OTHER_DESCRIPTION_FIELD from '@salesforce/schema/Claim_Product__c.Product_RTS_Other_Description__c';   // import Claim Product - Product RTS Other Description
import CLAIM_PRODUCT_QUANTITY_DIFF_FIELD from '@salesforce/schema/Claim_Product__c.CP_Quantity_Difference__c';                    // import Claim Product - CP Quantity Difference Field Schema
import CLAIM_PRODUCT_QUANTITY_RECEIVED_FIELD from '@salesforce/schema/Claim_Product__c.CP_Quantity_Received__c';                  // import Claim Product - CP Quantity Received Field Schema
import CLAIM_PRODUCT_ISSUE_DESCRIPTION_FIELD from '@salesforce/schema/Claim_Product__c.Issue_Description__c';                     // import Claim Product - Issue Description Field Schema
import CLAIM_NOTIFY_CUSTOMER_FIELD from '@salesforce/schema/Claim_Product__c.Notify_Customer__c';                                 // import Claim Product - Notify Customer Field Schema
import CLAIM_PRODUCT_JSON_FIELD from '@salesforce/schema/Claim_Product__c.JSON__c';                                               // import Claim Product - JSON Field Schema
import CLAIM_PRODUCT_IS_ADD_BACK_TO_STOCK_REQUIRED_FIELD from '@salesforce/schema/Claim_Product__c.Is_Add_Back_to_Stock_Required__c';  
import CLAIM_PRODUCT_INCORRECT_SKU_FIELD from '@salesforce/schema/Claim_Product__c.Incorrect_SKU__c';  
import CLAIM_PRODUCT_CREATED_RETURN_DATE_FIELD from '@salesforce/schema/Claim_Product__c.Created_Return_Date__c';  
import CLAIM_PRODUCT_RETURN_PALLET_FIELD from '@salesforce/schema/Claim_Product__c.Return_Pallet__c';

import CLAIM_PRODUCT_ACTION_ACTION_TYPE_FIELD from '@salesforce/schema/Claim_Product_Action__c.Action_Type__c';                  // import Claim Product Action - Action Type Field Schema
import CLAIM_PRODUCT_ACTION_CLAIM_PRODUCT_FIELD from '@salesforce/schema/Claim_Product_Action__c.Claim_Product__c';              // import Claim Product Action - Claim Product Field Schema
import CLAIM_PRODUCT_ACTION_JSON_FIELD from '@salesforce/schema/Claim_Product_Action__c.JSON__c';                                // import Claim Product Action - JSON Field Schema
import CLAIM_PRODUCT_ACTION_STATUS_FIELD from '@salesforce/schema/Claim_Product_Action__c.Status__c';                            // import Claim Product Action - Status Field Schema
import CLAIM_PRODUCT_ACTION_DESCRIPTION_FIELD from '@salesforce/schema/Claim_Product_Action__c.Description__c';                  // import Claim Product Action - Description Field Schema
import CLAIM_PRODUCT_ACTION_AMOUNT_FIELD from '@salesforce/schema/Claim_Product_Action__c.CP_Action_Amount__c';                  // import Claim Product Action - CP Action Amount Field Schema
import CLAIM_PRODUCT_ACTION_APPROVED_BY_DATETIME_FIELD from '@salesforce/schema/Claim_Product_Action__c.Approved_By_Datetime__c';                  // import Claim Product Action - Approved By Datetime     Field Schema
import CLAIM_PRODUCT_ACTION_APPROVED_BY_STAFF_MEMBER_FIELD from '@salesforce/schema/Claim_Product_Action__c.Approved_By_Staff_Member__c';          // import Claim Product Action - Approved By: Staff Member Field Schema

//assessment Form

import SELECTED_RECORD_TYPE_FIELD from '@salesforce/schema/Assessment_Form__c.Selected_Record_Type__c';          // import Claim Product Action - Approved By: Staff Member Field Schema



import Id from '@salesforce/user/Id'; //this scoped module imports the current user ID

import { getObjectInfo, getPicklistValues } from 'lightning/uiObjectInfoApi';                                               // import getObjectInfo and getPicklistValues from uiObjectInfoApi
import CLAIM_PRODUCT_OBJECT from '@salesforce/schema/Claim_Product__c';                                                     // import Claim Product Object Schema
import getSerialBatch from '@salesforce/apex/LightningUtilities.getSerialBatch';                                            // import getSerialBatch method from LightningUtilities Apex Class
import deleteUploadedFiles from '@salesforce/apex/LightningUtilities.deleteUploadedFiles';                                  // import deleteUploadedFiles method from LightningUtilities Apex Class
import getSerialBatchURL from '@salesforce/apex/LightningUtilities.getSerialBatchURL';                                      // import getSerialBatchURL method from LightningUtilities Apex Class
import handleClaimSubmitted from '@salesforce/apex/ClaimsManagementController.handleClaimSubmitted';                        // import handleClaimSubmitted method from ClaimsManagementController Apex Class
import getClaimProductActionsFromCaseId from '@salesforce/apex/ClaimsManagementController.getClaimProductActionsFromCaseId';// import getClaimProductActionsFromCaseId method from ClaimsManagementController Apex Class
import getClaimProductActionsFromCPId from '@salesforce/apex/ClaimsManagementController.getClaimProductActionsFromCPId';// import getClaimProductActionsFromCPId method from ClaimsManagementController Apex Class
import verifyManagerPin from '@salesforce/apex/LightningUtilities.verifyManagerPin';
import getUserInformation from '@salesforce/apex/LightningUtilities.getUserInformation';
import getStoreManagerList from '@salesforce/apex/LightningUtilities.getStoreManagerList';
import getClaimProductAction from '@salesforce/apex/LightningUtilities.getClaimProductAction';
import getShippingLocations from '@salesforce/apex/LightningUtilities.getShippingLocations';
import isCreatedByGuestUser from '@salesforce/apex/LightningUtilities.isCreatedByGuestUser';
import generatePdfContentAsBase64 from '@salesforce/apex/AssessmentFormPDFController.generatePdfContentAsBase64';

import { getRecord, getFieldValue } from 'lightning/uiRecordApi';
import USER_PROFILE_NAME from '@salesforce/schema/User.Profile.Name';
import USER_FIRST_NAME from '@salesforce/schema/User.FirstName';

import * as ldsUtils from 'c/ldsUtils';
import fetchClaimProductActionStatus from '@salesforce/apex/ClaimProductActionHelper.fetchClaimProductActionStatus';

export default class ClaimFormOrderItems extends LightningElement {

    @api erpData;                               // Order Data from the ERP.
    @api generatedJSON;                         // SObject Property of each objects.
    @api thirdScreenDetails = {};               // Details from the Order Product Screen.
    @api caseOwnerID;                           // Case Owner ID.
    @api caseOrigin;                            // Case Origin.
    @api userID;                                // Current User ID.
    @api bypassScreen = false;
    @api returnedLocationValue;
    @api userName;
    @api isChangeResolution = false;

    //Resume Claim process properties
    @api sfClaimProductActionID;
    @api sfClaimProductID;
    @api sfCaseID;
    @api sfReclaim = false;
    @api sfReclaimCaseId;

    isLoading = true;                           // Toggle for showing or hiding the Lightning Spinner.
    modalLoading = false;                       // Toggle for showing or hiding the Spinner on the Modal.
    showDialog = false;                         // Toggle for showing or hiding the Modal.
    claimProductRTID;                           // Default Record Type ID of Claim Product Object.
    resolutionOptions;                          // Resolution Options.
    uploadedFiles = [];                         // List of the Uploaded Files.
    orderItemList = [];                         // Order Item List.
    orderProductList = [];                      // Order Product List.
    skuList;                                    // List of SKUs.
    cardData = [];                              // List of Claim Product Data.
    tempCardData = [];                          // List of Claim Product Data for restoration purpose
    urlName;                                    // URL Name for the Knowledge Article.
    displayError = false;                       // Toggle for showing or hiding the Error Message.
    message = '';                               // Error Message.
    disableModalButtons = {                     // Toggle for enabling or disabling modal buttons.
        'confirm': false,
        'cancel': false
    };

    @track assessmentIds = false;
    @track nextPageError = '';
    @track data;
    @track currentItemIndex = 0;
    @track approvalScreen = false;
    @track testDataSetup = false;
    @track managerCode='';
    @track isLoadingInModel = false;
    @track pinFeedback = '';
    @track isPinError =false;
    @track showManagerApprovalModel = false;
    @track additionalFilters = ' Profile.Name IN (\'Claim Manager\', \'Store Manager\', \'System Administrator\')';
    @track selectedManager = undefined;
    @track selectedManagerName = '';
    @track disableManagerSelection = false;
    @track foundManagerFromApex = false;
    @track managersList = [];
    @track storeManagersList = [];
    @track bypassApprovalForProfiles = ['Claim Manager', 'Claim User', 'Claims Agent'];
    @track isLoadingManagerList = false;
    @track selectedManagerFromList = '';
    @track selectedManagerNameFromList = '';
    @track isGuest = true;

    @track isWarehouseOrStoreProfile = false;
    @track showNoProductErrorModal = false;
    @track  firstName = '';
    @track caseTypeOptions = [];
    @track  caseTypeToAssign = '';
    @track selectedRecordType;
    


    userId = Id;
    @track userProfileName;

    @track recordIds = [];          //used in multiple processing of cpa combined

    //ProcessPanel related changes
    @track currentCPAId = '';
    @track showProcessPanel = false;
    @track currentProcessingStep = 0; 
    @track processSteps = [];
    @track showFooterButtonsOnProcessingModal = false;
    @track showCloseClaimButton = true;

    @track ProductQunatityDetailsParent = [];
    @track ProductQunatityDetailsParentAfterDuplicates = [];
    @track getArrayVal = [];

    @track printRecordId;
    @track showPrintReturnLabelWindow = false;
    @track showPrintIdentificationLabelWindow = false;
    @track identificationPrinted = [];

    preferredResolutionPicklistMapping = {
        'Replacement Item': 'Full Replacement',
        'Replacement Part': 'Replacement Part',
        'Store Credit': 'Store Credit',
        'Money Back': 'Refund',
        'Warranty Order - Part': 'Replacement Part',
        'Warranty Order': 'Full Replacement',
        'Item Returned': 'Item returned'
    };

    resolutionActionPicklistMapping = {
        'Replacement Item': 'Replace',
        'Replacement Part': 'Replace Part',
        'Store Credit': 'Store Credit',
        'Money Back': 'Refund Full',
        'Warranty Order - Part': 'Replace Part',
        'Warranty Order': 'Replace',
        'Item Returned': 'Return/NoAction'

    };

    changeOfMindSubCategoryOptions = [
        {
            label:'Advertised at a lower price',
            value:'Advertised at a lower price'
        },
        {
            label:'Product Not As Described',
            value:'Product Not As Described'
        },
        {
            label:'Does not suit customer needs',
            value:'Does not suit customer needs'
        },
        {
            label:'Other',
            value:'Other'
        },
        {
            label:'Not aware Pre Order Date',
            value:'Not aware Pre Order Date'
        },
    ];

    productIssueProductRecallSubCategoryOptions = [
        {
            label:'Store Credit',
            value:'Store Credit'
        },
        {
            label:'Replacement Item',
            value:'Replacement Item'
        },
        {
            label:'Money Back',
            value:'Money Back'
        }
    ];

    transitAndDeliveryIssueSubCategoryOptions = [
        {
            "label": "Product not dispatched",
            "value": "Product not dispatched"
        },
        {
            "label": "Product dispatched late (over 48 hours)",
            "value": "Product dispatched late (over 48 hours)"
        },
        {
            "label": "Damaged in Transit",
            "value": "Damaged in Transit"
        },
        {
            "label": "Lost in Transit",
            "value": "Lost in Transit"
        },
        {
            "label": "Returned to Sender",
            "value": "Returned to Sender"
        },
        {
            "label": "Wrong Product Dispatched",
            "value": "Wrong Product Dispatched"
        },
        {
            "label": "Wrong Product Quantity Dispatched",
            "value": "Wrong Product Quantity Dispatched"
        }
    ]

    orderIssueSubCategoryOptions = [
        {
            "label": "Duplicate Order",
            "value": "Duplicate Order"
        },
        {
            "label": "Pre-Order Delays",
            "value": "Pre-Order Delays"
        },
        {
            "label": "Orderded Wrong Product",
            "value": "Orderded Wrong Product"
        },
        {
            "label": "Discontinued Product(Sold Out/No Longer Available)",
            "value": "Discontinued Product(Sold Out/No Longer Available)"
        },
        {
            "label": "Cancel Order",
            "value": "Cancel Order"
        }
    ]

    get disableClaim(){
        let temp = this.processSteps.filter(val=> val.showIdentifictionLabel);
        return this.identificationPrinted.length != temp.length;
    }

    /************************ get data methods  */
    connectedCallback() {
        console.log('asseemt later@@@'+JSON.stringify(this.data));
      
        if(this.bypassScreen == true) {
            
            this.isLoading = true;
            setTimeout(() => {
                this.isLoading = true;
                this.handleFinalSubmit();
            }, 1000);
        }
        this.fetchUserInformation();
        this.assignProcessingSteps();
        this.isCommunityUser();
       
    }
isCommunityUser(){
    console.log('sfCaseID@@@'+this.sfCaseID);
    if (this.sfCaseID) {
        isCreatedByGuestUser({ caseId: this.sfCaseID })
            .then(result => {
                this.isGuest = result;
                console.log('Is created by guest user:', result);
            })
            .catch(error => {
                console.error('Error checking guest user:', error);
            });
    }
}
    assignProcessingSteps() {
        if(this.sfClaimProductActionID == undefined || this.sfClaimProductActionID == '') {
            this.processSteps = [
                { label: "Creating Claim", isCompleted: false, isProcessing: true, isFailed:false, isSkipped: false, index: 0, showErrorMessage: false, errorMessage: '', showCopyToClipboard: false, showLinkToTools: false, showPrintReturn: false},
                { label: "Creating Claim Product(s), Claim Product Action(s)", isCompleted: false, isProcessing: false, isFailed:false, isSkipped: false, index: 1, showErrorMessage: false, errorMessage: '', showCopyToClipboard: false, showLinkToTools: false, showPrintReturn: false},
               
            ];
        } else {
            this.processSteps = [
                { label: "Updating Claim", isCompleted: false, isProcessing: true, isFailed:false, isSkipped: false, index: 0, showErrorMessage: false, errorMessage: '', showCopyToClipboard: false, showLinkToTools: false, showPrintReturn: false},
                { label: "Updating Claim Product(s), Claim Product Action(s)", isCompleted: false, isProcessing: false, isFailed:false, isSkipped: false, index: 1, showErrorMessage: false, errorMessage: '', showCopyToClipboard: false, showLinkToTools: false, showPrintReturn: false},
                
            ];
        }
    }

    @wire(getRecord, { recordId: '$userID', fields: [USER_PROFILE_NAME,USER_FIRST_NAME]})
    getUserRecordData({ error, data }) {
        if (data) {
            let userProfileName = getFieldValue(data, USER_PROFILE_NAME);
            this.firstName = getFieldValue(data, USER_FIRST_NAME);
            console.log('userProfileName -> ' + JSON.stringify(userProfileName, null, 2));
            if(userProfileName === 'Warehouse') {
                this.isWarehouseOrStoreProfile = true;
            } else {
                this.isWarehouseOrStoreProfile = false;
            }
        } else if (error) {
            console.error('Error fetching loggedin User Info', error);
        }
    }
    @wire(getShippingLocations)
    getShippingLocations({ error, data }) {
        if (data) {
          
            this.locationMap = data.map(location => ({
                label: location.Locations__c, 
                value: location.Locations__c 
            }));
            this.locationMapRes = data.reduce((map, location) => {
                const cityName = location.Locations__c;
                
                if (cityName) {  
                    map[cityName] = cityName;
                } 
                return map;
            }, {});
          
            console.log('All Location Map:', JSON.stringify(this.locationMap));
        } else if (error) {
            console.error('Error fetching shipping locations:', error);
        } 

    }
    @wire(getObjectInfo, { objectApiName: CASE_OBJECT })
    objectInfo;

    @wire(getPicklistValues, {
        fieldApiName: CASE_TYPE_FIELD,
        recordTypeId: '$objectInfo.data.defaultRecordTypeId'
    })
    caseTypePicklistValues({ data, error }) {
        if (data) {
            this.caseTypeOptions = data.values;
            console.log('Case Type picklist values:', JSON.stringify(this.caseTypeOptions, null, 2));
        } else if (error) {
            console.error('Error fetching picklist values:', error);
        }
    }
     assignCaseTypeBasedOnLocation() {
    const matchingLocation = this.locationMap.find(location =>
        location.value.toLowerCase() === this.firstName.toLowerCase()
    );

    if (matchingLocation) {
        this.caseTypeToAssign = `Claims Store (${matchingLocation.value})`;
        console.log('Assigned Case Type:', this.caseTypeToAssign);
       
    } 
    else 
    { 
        if(this.userProfileName == 'Claims Agent' || this.userProfileName == 'Claim Manager' || this.userProfileName == 'Claim User')
        {
            this.caseTypeToAssign = 'Canberra Call Centre';
        }
        else{
            this.caseTypeToAssign = 'Claims Department';
        }


         /*getPublicGroupsAndMembers()
        .then(result => {
            console.log('Result from public group check: ' + JSON.stringify(result));
            if (result === true) {
                this.isUserMember = true; 
                this.caseTypeToAssign = 'Canberra Call Centre';
               
            } else {
                this.isUserMember = false; 
                this.caseTypeToAssign = null;
             
            }
        })
        .catch(error => {
            this.error = error;
            console.error('Error checking user group membership:', error);
            this.caseTypeToAssign = null;

        });
return this.caseTypeToAssign;*/
      
    }
}
     

    @wire(getSerialBatchURL)
    getSerialBatchURL({ data, error }) {
        if (data) {
            this.urlName = data;
        } else if (error) {
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
        }
    }

    @wire(getObjectInfo, { objectApiName: CLAIM_PRODUCT_OBJECT })
    claimProductObjectInfo({ data, error }) {
        if (data) {
            this.claimProductRTID = data.defaultRecordTypeId;
        } else if (error) {
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
        }
    }

    @wire(getPicklistValues, { recordTypeId: '$claimProductRTID', fieldApiName: CLAIM_PRODUCT_RESOLUTION_FIELD })
    claimProductResolution({ data, error }) {
        if (data) {
            this.resolutionOptions = data.values;
            if (this.thirdScreenDetails) {
                this.orderItemList = this.thirdScreenDetails.orderItemList;
                this.orderProductList = this.thirdScreenDetails.orderProductList;
            }
            this.retrieveSKUs();
        } else if (error) {
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
        }
    }

    retrieveSKUs() {

        let orderItemList = this.orderItemList;
        let skuList = [];

        for (let i = 0; i < orderItemList.length; i++) {
            skuList.push(orderItemList[i].data.sku);
        }

        this.skuList = [...new Set(skuList)];

    }

    @wire(getSerialBatch, { skuList: '$skuList' })
    getSerialBatch({ data, error }) {
        if (data) {
            if(this.sfClaimProductActionID == undefined && this.sfCaseID == undefined) {
                this.formatCardData(data);
                if(this.bypassScreen != true) {
                    this.isLoading = false;
                }
            } else {
                this.isLoading = true;
                this.fetchClaimProductAction();
            }
        } else if (error) {
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
        }
    }

    /******************************************* */

    fetchClaimProductAction() {
        getClaimProductAction({ cpaId: this.sfClaimProductActionID })
            .then(result => {
               console.log('result -> ' + JSON.stringify(result, null, 2));
               this.data = JSON.parse(result.JSON__c);
               this.cardData = [];
               this.data.shippingTo = '';
               this.data.shippingToName = '';
               if(this.isGuest == false){
                this.data.shipToAddress = this.data.shipFromAddress != undefined ? this.data.shipFromAddress : {};

               }
            //this.data.shipToAddress = this.data.shipFromAddress != undefined ? this.data.shipFromAddress : this.data.shipToAddress;

            //    this.data.shipToAddress = {};
               if(this.data.assesmentPossibleDisabled != undefined && this.data.assesmentPossibleDisabled == true && this.data.selectedIssueSubCategory == 'Defective/Faulty Product') {
                    this.data.assesmentPossibleDisabled = false;
                    this.data.assesmentPossible = 'Now';
                    this.data.isPendingAssessment = false;
                    this.data.hasApprovalStatus = false;
               }
               this.cardData.push(this.data);
               console.log('result 23 -> ' + JSON.stringify(this.data, null, 2));
               this.isLoading = false;
            })
            .catch(error => {
                this.isLoading = false;

                let formattedError = ldsUtils.reduceErrors(error);
                this.showError(formattedError);
            });
    }

    fetchUserInformation() {
        getUserInformation({})
            .then(result => {
                console.log('getUserInformation result -> ' + JSON.stringify(result, null, 2));
                if(result.storeManager != undefined && result.storeManager != null) {
                    this.selectedManagerName = result.storeManager.Name;
                    this.selectedManager = result.storeManager.Id;
                    this.disableManagerSelection = true;
                    this.foundManagerFromApex = true;

                    this.getStoreManagerListFromApex();
                } else {
                    this.selectedManagerName = result.fullname;
                    this.selectedManager = result.userId;
                }
                this.userProfileName = result.profileName;
                console.log('this.userProfileName -> ' + JSON.stringify(this.userProfileName, null, 2));
                if(result.otherUsers != undefined && result.otherUsers != null && result.otherUsers.length > 0) {
                    let managers = [];
                    result.otherUsers.forEach(userObj => {
                        managers.push({
                            'label' : userObj.Name,
                            'value': userObj.Id
                        })
                    });
                    this.managersList = managers;
                }
            })
            .catch(error => {
                this.isPinError = true;
                // Handle errors
                this.isLoadingInModel = false;
                this.pinFeedback = error.body.message; // Show error message from the server
            });
    }

    getStoreManagerListFromApex() {
        this.isLoadingManagerList = true;
        getStoreManagerList({storeManagerUserId : this.selectedManager})
        .then(result => {
            console.log('getStoreManagerList result -> ' + JSON.stringify(result, null, 2));
            let managers = [];
            result.forEach(userObj => {
                managers.push({
                    'label' : userObj.Store_Manager_Name__c,
                    'value': userObj.Id
                })
            });
            this.storeManagersList = managers;
            this.isLoadingManagerList = true;
        })
        .catch(error => {
            this.storeManagersList = [];
            this.isLoadingManagerList = false;
        });
    }

    formatCardData(data) {
        console.log('700 data -> ' , JSON.parse(JSON.stringify(data)));
        let orderItemList = this.orderItemList;
        let cardData = [];
       
        // debugger;
        console.log('586 orderItemList -> ' + JSON.stringify(orderItemList, null, 2));

        for (let i = 0; i < orderItemList.length; i++) {
            let orderItemData = orderItemList[i].data;

            let attributes = {
                'id': i + ';' + orderItemData.sku,
                'itemID': orderItemData.item_id,
                'sku': orderItemData.sku,
                'prodName': orderItemData.name,
                'serialBatchLabel': 'Enter Serial / Batch Number',
                'batchLabel': 'Enter Batch Number',
                'batchValue': orderItemData.batch_number != null ? orderItemData.batch_number : '',
                'serialLabel': 'Enter Serial Number',
                'serialValue': '',
                'cloneSerialValue': '',
                'cloneBatchValue': '',
                'serialVisible': true,
                'batchVisible': true,
                'serialRequired': false,
                'batchRequired': false,
                'selectedResolution': '',
                'isValid': false,
                'uploadedFiles': [],
                'uniqueID': orderItemList[i].uniqueID,
                'itemCost': orderItemData.item_cost,
                'itemPrice': orderItemData.item_price.toFixed(2),
                'isChild': orderItemList[i].isChild,
                'hasApprovalStatus': false,
                'isApproved': false,
                'didNotCauseDamage': undefined,
                'readyForApproval': true,
                'order_date': this.erpData.order_date,
                'is_pickedup': orderItemData.is_pickedup,
                'qty': orderItemData.qty,
                'qty_shipped': orderItemData.wms_shipped_qty,
                'qty_refundable': orderItemData.refundable_qty,
                'shipFromAddress': {},
                'isShipped': orderItemData.isShipped,
                'isPacked': orderItemData.isPacked
            };

            if(orderItemData.sku == 'FREIGHT') {
                attributes.selectedIssueCategory = 'Transit/Delivery Issue';
                attributes.selectedIssueSubCategory = 'Shipping';
                //attributes.resolutionOutcome = 'Store Credit';
                attributes.freightPaid = orderItemData.item_price.toFixed(2);
            }

            if((attributes.isShipped != undefined && attributes.isShipped == true)) {
                attributes.shipmentStatus = 'Shipped';
            } else {
                attributes.shipmentStatus = 'Pending to Ship';
            }

            let serialMandatory = false;
            let batchMandatory = false;
            let floorPrice = undefined;
            let warrantyClaimDCDisposalCompulsory = false;
            let assessmentFormRequired = false;

            if (data.hasOwnProperty(orderItemData.sku)) {
                serialMandatory = data[orderItemData.sku].Serial_Mandatory__c;
                batchMandatory = data[orderItemData.sku].Batch_Mandatory__c;
                floorPrice = data[orderItemData.sku].Floor_Price__c;
                warrantyClaimDCDisposalCompulsory = data[orderItemData.sku].Warranty_Claim_DC_Disposal_Compulsory__c;
                assessmentFormRequired = data[orderItemData.sku].Assessment_Form_Required__c;
                if(data[orderItemData.sku].Product_Category__r){
                console.log('battrey table--',data[orderItemData.sku]);

                    attributes.isBattrey = data[orderItemData.sku].Product_Category__r.Name.toLowerCase().includes('bush power');
                }
                console.log('battrey table--',data[orderItemData.sku]);
                attributes.battreyInfo = data[orderItemData.sku].Battrey_Capacity__r;
            }

            attributes.floorPrice = floorPrice;
            attributes.warrantyClaimDCDisposalCompulsory = warrantyClaimDCDisposalCompulsory;
            attributes.assessmentFormRequired = assessmentFormRequired;

            if (!serialMandatory) {
                attributes.serialRequired = false;
                attributes.serialVisible = false;
            } else if (serialMandatory) {
                attributes.serialValue = '';
                attributes.cloneSerialValue = '';
                attributes.serialRequired = true;
            }

            if (!batchMandatory) {
                attributes.batchRequired = false;
                if(orderItemData.batch_number == 'N/A' || orderItemData.batch_number == '') {
                    attributes.batchVisible = false;
                }
            } else if (batchMandatory) {
                attributes.batchValue = orderItemData.batch_number;
                attributes.cloneBatchValue = orderItemData.batch_number;
                attributes.attributes = false;
                attributes.batchRequired = true;
            }


            cardData.push(attributes);

        }

        console.log('673 orderItemList -> ' + JSON.stringify(orderItemList, null, 2));
        console.log('cardData -> ' + JSON.stringify(cardData, null, 2));

        if(this.testDataSetup == true){
            cardData = JSON.parse('[{"id":"0;AKAC-COFFEECUP_01","itemID":"4118400","sku":"AKAC-COFFEECUP_01","prodName":"Kings Blue Coffee Cup | 380ml Vacuum Travel Mug | Insulated Stainless Steel | For Hot & Cold Drinks","serialBatchLabel":"Enter Serial / Batch Number","batchLabel":"Enter Batch Number","batchValue":"","serialLabel":"Enter Serial Number","serialValue":"","cloneSerialValue":"","cloneBatchValue":"","serialVisible":false,"batchVisible":true,"serialRequired":false,"batchRequired":false,"selectedResolution":"","isValid":false,"uploadedFiles":[],"uniqueID":"0-0.2446148213098409-7/5/2024 @ 11:41:31:595","itemCost":4.28,"itemPrice":12.95,"isChild":false,"shipmentStatus":"Shipped","storeShipmentDate":"2024/5/1","dcShipmentDate":"2024/4/4","recalled":true,"channelOfOrder":"Store","selectedIssueCategory":"30 Day Change of Mind","selectedIssueSubCategory":"Advertised at a lower price"},{"id":"1;AKAC-COFFEECUP_01","itemID":"4118400","sku":"AKAC-COFFEECUP_01","prodName":"Kings Blue Coffee Cup | 380ml Vacuum Travel Mug | Insulated Stainless Steel | For Hot & Cold Drinks","serialBatchLabel":"Enter Serial / Batch Number","batchLabel":"Enter Batch Number","batchValue":"","serialLabel":"Enter Serial Number","serialValue":"","cloneSerialValue":"","cloneBatchValue":"","serialVisible":false,"batchVisible":true,"serialRequired":false,"batchRequired":false,"selectedResolution":"","isValid":false,"uploadedFiles":[],"uniqueID":"0-0.2446148213098409-7/5/2024 @ 11:41:31:595","itemCost":4.28,"itemPrice":12.95,"isChild":false,"shipmentStatus":"Shipped","storeShipmentDate":"2023/4/4","dcShipmentDate":"2023/4/4","recalled":false,"channelOfOrder":"Website","selectedIssueCategory":"30 Day Change of Mind","selectedIssueSubCategory":""},{"id":"2;AKTA-AWN2X2.5_B_02","itemID":4118406,"sku":"AKTA-AWN2X2.5_B_02","prodName":"Kings 2x2.5m Side Awning | UPF50+ | 170gsm Waterproof | Suits All Vehicles","serialBatchLabel":"Enter Serial / Batch Number","batchLabel":"Enter Batch Number","batchValue":"","serialLabel":"Enter Serial Number","serialValue":"","cloneSerialValue":"","cloneBatchValue":"","serialVisible":false,"batchVisible":true,"serialRequired":false,"batchRequired":false,"selectedResolution":"","isValid":false,"uploadedFiles":[],"uniqueID":"1-0.****************-7/5/2024 @ 11:41:31:595","itemCost":62.99,"itemPrice":139.89,"isChild":true,"shipmentStatus":"Shipped","storeShipmentDate":"2024/5/1","dcShipmentDate":"2024/4/4","recalled":true,"channelOfOrder":"Store","selectedIssueCategory":"Product Issue","selectedIssueSubCategory":"Replacement Item"},{"id":"3;AKTA-AWN2X2.5_B_02","itemID":4118406,"sku":"AKTA-AWN2X2.5_B_02","prodName":"Kings 2x2.5m Side Awning | UPF50+ | 170gsm Waterproof | Suits All Vehicles","serialBatchLabel":"Enter Serial / Batch Number","batchLabel":"Enter Batch Number","batchValue":"","serialLabel":"Enter Serial Number","serialValue":"","cloneSerialValue":"","cloneBatchValue":"","serialVisible":false,"batchVisible":true,"serialRequired":false,"batchRequired":false,"selectedResolution":"","isValid":false,"uploadedFiles":[],"uniqueID":"1-0.****************-7/5/2024 @ 11:41:31:595","itemCost":62.99,"itemPrice":139.89,"isChild":true,"selectedIssueCategory":"Transit/Delivery Issue","selectedIssueSubCategory":"Damaged in Transit"},{"id":"4;AKTA-AWN_WALL_2.5","itemID":4118409,"sku":"AKTA-AWN_WALL_2.5","prodName":"Adventure Kings Awning Side Wall | Waterproof | 2900mm x 2500mm ","serialBatchLabel":"Enter Serial / Batch Number","batchLabel":"Enter Batch Number","batchValue":"","serialLabel":"Enter Serial Number","serialValue":"","cloneSerialValue":"","cloneBatchValue":"","serialVisible":false,"batchVisible":true,"serialRequired":false,"batchRequired":false,"selectedResolution":"","isValid":false,"uploadedFiles":[],"uniqueID":"1-0.****************-7/5/2024 @ 11:41:31:595","itemCost":16.34,"itemPrice":36.29,"isChild":true,"selectedIssueCategory":"30 Day Change of Mind","selectedIssueSubCategory":"Product Not As Described","pricePaid":"damaged"},{"id":"5;AKTA-AWN_WALL_2.5","itemID":4118409,"sku":"AKTA-AWN_WALL_2.5","prodName":"Adventure Kings Awning Side Wall | Waterproof | 2900mm x 2500mm ","serialBatchLabel":"Enter Serial / Batch Number","batchLabel":"Enter Batch Number","batchValue":"","serialLabel":"Enter Serial Number","serialValue":"","cloneSerialValue":"","cloneBatchValue":"","serialVisible":false,"batchVisible":true,"serialRequired":false,"batchRequired":false,"selectedResolution":"","isValid":false,"uploadedFiles":[],"uniqueID":"1-0.****************-7/5/2024 @ 11:41:31:595","itemCost":16.34,"itemPrice":36.29,"isChild":true,"selectedIssueCategory":"Order Issue","selectedIssueSubCategory":"Cancel Order"},{"id":"6;AKTA-AWN_WALL_2.5","itemID":4118409,"sku":"AKTA-AWN_WALL_2.5","prodName":"Adventure Kings Awning Side Wall | Waterproof | 2900mm x 2500mm ","serialBatchLabel":"Enter Serial / Batch Number","batchLabel":"Enter Batch Number","batchValue":"","serialLabel":"Enter Serial Number","serialValue":"","cloneSerialValue":"","cloneBatchValue":"","serialVisible":false,"batchVisible":true,"serialRequired":false,"batchRequired":false,"selectedResolution":"","isValid":false,"uploadedFiles":[],"uniqueID":"1-0.****************-7/5/2024 @ 11:41:31:595","itemCost":16.34,"itemPrice":36.29,"isChild":true,"selectedIssueCategory":"30 Day Change of Mind","selectedIssueSubCategory":"Does not suit customer needs","pricePaid":"new model came"},{"id":"7;AKTA-AWN_WALL_2.5","itemID":4118409,"sku":"AKTA-AWN_WALL_2.5","prodName":"Adventure Kings Awning Side Wall | Waterproof | 2900mm x 2500mm ","serialBatchLabel":"Enter Serial / Batch Number","batchLabel":"Enter Batch Number","batchValue":"","serialLabel":"Enter Serial Number","serialValue":"","cloneSerialValue":"","cloneBatchValue":"","serialVisible":false,"batchVisible":true,"serialRequired":false,"batchRequired":false,"selectedResolution":"","isValid":false,"uploadedFiles":[],"uniqueID":"1-0.****************-7/5/2024 @ 11:41:31:595","itemCost":16.34,"itemPrice":36.29,"isChild":true,"selectedIssueCategory":"30 Day Change of Mind","selectedIssueSubCategory":"Advertised at a lower price","priceAdvertised":"21","pricePaid":"35","creditDueAmount":14}]');
            this.currentItemIndex = 7;
        }

        this.cardData = cardData;
        this.tempCardData = JSON.parse(JSON.stringify(cardData));
        this.data = this.cardData[0];
    }

    // this method need to be corrected / optimised -- sun 22 sept 2024
    handleClick(event) {

        try {

            let buttonName = event.target.dataset.id;

            if (buttonName == 'previous') {

                let cardData = JSON.parse(JSON.stringify(this.cardData));

                if (cardData.length > 0) {
                    this.showDialog = true;
                } else {

                    let pageName = 'orderProducts';
                    let erpData = this.erpData;

                    this.sendNavigateEvent(pageName, erpData, null);

                }

            } else if (buttonName == 'next') {
                if(!this.validateInput()) {
                    this.showError('Please check that all required fields are filled out correctly and that there is no incorrect data');
                    // this.nextPageError = 'Please check that all required fields are filled out correctly and that there is no incorrect data';
                    return;
                }

                if(this.cardData[this.currentItemIndex].selectedIssueCategory == 'Product Issue' &&
                    this.cardData[this.currentItemIndex].selectedIssueSubCategory == 'Defective/Faulty Product' &&     
                    this.cardData[this.currentItemIndex].isIssueResolved == 'No' &&
                    this.cardData[this.currentItemIndex].assesmentPossible != 'Later' &&
                    this.cardData[this.currentItemIndex].assessmentFormRequired == true &&
                    (this.cardData[this.currentItemIndex].assessmentTableData == undefined || this.cardData[this.currentItemIndex].assessmentTableData.length == 0)
                ) {
                    this.showError('Assessment Form is required for this product. Please fill the Assessment Form before proceeding');
                    // this.message = 'Please select atleast 1 product to proceed.';
                    // this.displayError = true;
                    // this.isLoading = false;
                    return false;
                }

                if(
                   (this.cardData[this.currentItemIndex].selectedIssueSubCategory == 'Part Missing' && this.cardData[this.currentItemIndex].isSpareAvailableInTable == 'Yes' ) ||
                    (
                        this.cardData[this.currentItemIndex].resolutionOutcome == 'Replacement Part' &&
                        this.cardData[this.currentItemIndex].selectedIssueSubCategory == 'Defective/Faulty Product' &&
                        this.cardData[this.currentItemIndex].didCauseDamage != 'Yes' &&
                        this.cardData[this.currentItemIndex].assesmentPossible != 'Later'
                    ) 
                ) {
                    let singleCardObj = this.cardData[this.currentItemIndex];
                    console.log('coming to part123');
                    console.log('singleCardObj@@@@123'+JSON.stringify(singleCardObj));
                    if(singleCardObj.replacements != undefined && singleCardObj.replacements.length > 0){
                        let foundProdWithEligibleQuantity = false;
                        let replacementsToRemove = [];

                        for (let index = 0; index < singleCardObj.replacements.length; index++) {
                            let element = singleCardObj.replacements[index];
                            console.log('element@@@@123'+JSON.stringify(element));
                            if(parseInt(element.qtyToOrder) > 0) {
                                foundProdWithEligibleQuantity = true;
                            } else {
                                replacementsToRemove.push(element);
                                console.log('replacementsToRemove@@@@123'+JSON.stringify(replacementsToRemove));
                            }
                        }

                        if(!foundProdWithEligibleQuantity) {
                            this.showError('Please select atleast 1 product to proceed.');
                            // this.message = 'Please select atleast 1 product to proceed.';
                            // this.displayError = true;
                            // this.isLoading = false;
                            return false;
                        }
                        if(replacementsToRemove.length > 0) {
                            singleCardObj.replacements = singleCardObj.replacements.filter(element => !replacementsToRemove.includes(element));
                            this.cardData[this.currentItemIndex] = JSON.parse(JSON.stringify(singleCardObj));
                        }

                        this.message = '';
                        this.displayError = false;
                    } else {
                        this.showError('Please select atleast 1 product to proceed.');
                        // this.message = 'Please select atleast 1 product to proceed.';
                        // this.displayError = true;
                        // this.isLoading = false;
                        return false;
                    }
                }

                //Auto-Approving items for warehouse profile
                if(this.userProfileName == 'Warehouse') {
                    this.cardData[this.currentItemIndex].isApproved = true;
                    this.cardData[this.currentItemIndex].hasApprovalStatus = true;
                }
                console.log('945 this.cardData -> ' + JSON.stringify(this.cardData, null, 2));

                //VALID CLAIM LOGIC
                if(
                    (this.cardData[this.currentItemIndex].selectedIssueSubCategory == '' || this.cardData[this.currentItemIndex].selectedIssueSubCategory == undefined) &&
                    ( this.cardData[this.currentItemIndex].selectedOrderIssueSubCategory == '' || this.cardData[this.currentItemIndex].selectedOrderIssueSubCategory == undefined)
                    &&
                    this.cardData[this.currentItemIndex].selectedIssueCategory != 'Associated Product'
                    ) {
                        this.cardData[this.currentItemIndex].readyForApproval = false;
                }

                this.message = '';
                this.displayError = false;
                if(this.currentItemIndex == this.cardData.length -1) {
                    this.isLoading = true;
                    this.showDialog = false;
                    this.message = '';
                    this.displayError = false;

                    let hasSerialBatch = ldsUtils.validateForm(this, 'lightning-input');
                    let hasResolution = ldsUtils.validateForm(this, 'lightning-combobox');
                    let cardData = JSON.parse(JSON.stringify(this.cardData, null, 2));

                    let errorCounter = 0;

                    if (!this.isCamper) {
                        cardData.forEach(function(obj) {
                            if (obj.uploadedFiles.length == 0) {
                                errorCounter++;
                            }
                        });
                    }

                    if (hasSerialBatch && hasResolution && errorCounter == 0) {
                        this.showProcessPanel = true;
                        this.message = '';
                        this.displayError = false;
                        this.handleFinalSubmit();
                    } else {

                        let message = 'Please correct the errors on the claimed items to proceed.';
                        this.showError(message);

                    }
                } else {
                    this.message = '';
                    this.displayError = false;
                    this.currentItemIndex = this.currentItemIndex+1;
                    this.data = this.cardData[this.currentItemIndex];
                }

            } else if(buttonName == 'previousItem') {
                if(this.approvalScreen == true) {
                    this.approvalScreen = false;
                    this.currentItemIndex = this.cardData.length-1;
                } else {
                    this.currentItemIndex = this.currentItemIndex-1;
                }
                this.data = this.cardData[this.currentItemIndex];
            } else if(buttonName == 'nextPage') {
                if(!this.validateInput()) {
                    return;
                }
                
                if(this.cardData[this.currentItemIndex].selectedIssueCategory == 'Product Issue' &&
                    this.cardData[this.currentItemIndex].selectedIssueSubCategory == 'Defective/Faulty Product' &&     
                    this.cardData[this.currentItemIndex].isIssueResolved == 'No' &&
                    this.cardData[this.currentItemIndex].assesmentPossible != 'Later' &&
                    this.cardData[this.currentItemIndex].assessmentFormRequired == true &&
                    (this.cardData[this.currentItemIndex].assessmentTableData == undefined || this.cardData[this.currentItemIndex].assessmentTableData.length == 0)
                ) {
                    this.showError('Assessment Form is required for this product. Please fill the Assessment Form before proceeding');
                    // this.message = 'Please select atleast 1 product to proceed.';
                    // this.displayError = true;
                    // this.isLoading = false;
                    return false;
                }

                if(
                    (this.cardData[this.currentItemIndex].selectedIssueSubCategory == 'Part Missing' && this.cardData[this.currentItemIndex].isSpareAvailableInTable == 'Yes' ) ||
                   (
                        this.cardData[this.currentItemIndex].resolutionOutcome == 'Replacement Part' &&
                        this.cardData[this.currentItemIndex].selectedIssueSubCategory == 'Defective/Faulty Product'
                    )
                    && this.cardData[this.currentItemIndex].assesmentPossible != 'Later' 
                    && this.cardData[this.currentItemIndex].assesmentPossible != undefined
                    && this.cardData[this.currentItemIndex].assesmentPossible != ''
                ) {
                    let singleCardObj = this.cardData[this.currentItemIndex];
                    console.log('singleCardObj@@@@'+JSON.stringify(singleCardObj));
                    if(singleCardObj.replacements != undefined && singleCardObj.replacements.length > 0){
                        let foundProdWithEligibleQuantity = false;
                        let replacementsToRemove = [];
                        for (let index = 0; index < singleCardObj.replacements.length; index++) {
                            let element = singleCardObj.replacements[index];
                            console.log('element@@@@@'+JSON.stringify(element));
                            if(parseInt(element.qtyToOrder) > 0) {
                                foundProdWithEligibleQuantity = true;
                            } else {
                                replacementsToRemove.push(element);
                                console.log('replacementsToRemove@@@@@'+JSON.stringify(replacementsToRemove));
                            }
                        }

                        if(!foundProdWithEligibleQuantity) {
                            this.showError('Please select atleast 1 product to proceed.');
                            // this.message = 'Please select atleast 1 product to proceed.';
                            // this.displayError = true;
                            // this.isLoading = false;
                            return false;
                        }
                        if(replacementsToRemove.length > 0) {
                            singleCardObj.replacements = singleCardObj.replacements.filter(element => !replacementsToRemove.includes(element));
                            this.cardData[this.currentItemIndex] = JSON.parse(JSON.stringify(singleCardObj));
                        }
                        this.message = '';
                        this.displayError = false;
                    } else {
                        this.showError('Please select atleast 1 product to proceed.');
                        // this.message = 'Please select atleast 1 product to proceed.';
                        // this.displayError = true;
                        // this.isLoading = false;
                        return false;
                    }
                }
                if(this.approvalScreen == true) {
                    //Checking if all products has approval status
                    let isAllAssociatedProducts
                    let foundCPWithoutApprovalStatus = false;
                    //if(this.userProfileName != 'Claims Management Profile'){
                        isAllAssociatedProducts = true;
                    //}
                   
                    let cardDataLocal = JSON.parse(JSON.stringify(this.cardData));
                    let isProductFaultItemAvailableWithAssessmentLater = false;
                    let isAnyOtherItemAvailable = false;

                    cardDataLocal.forEach(element => {
                        if( (element.hasApprovalStatus == undefined || element.hasApprovalStatus == false )
                            &&
                            (element.assesmentPossible == undefined || (element.assesmentPossible != undefined && element.assesmentPossible != 'Later') )
                            && element.readyForApproval == true
                        ) {
                            foundCPWithoutApprovalStatus = true;
                        }

                        if(element.selectedIssueCategory != 'Associated Product') {
                            isAllAssociatedProducts = false;
                        }

                        if(element.selectedIssueCategory  == 'Product Issue' && element.selectedIssueSubCategory == 'Defective/Faulty Product' && (element.assesmentPossible == 'Later' || (element.assessmentTableData.length > 0 && element.assessmentTableData[0].Assessment_Outcome__c == 'Draft'))) {
                            isProductFaultItemAvailableWithAssessmentLater = true;
                        } else {
                            isAnyOtherItemAvailable = true;
                        }

                        //Auto-Approving items for warehouse profile
                        if(this.userProfileName == 'Warehouse') {
                            element.isApproved = true;
                            element.hasApprovalStatus = true;
                        }
                    });
                    this.cardData = JSON.parse(JSON.stringify(cardDataLocal));
                    console.log('1075 this.cardData -> ' + JSON.stringify(this.cardData, null, 2));
                    console.log('isAllAssociatedProducts@@'+isAllAssociatedProducts);
                    if(isAllAssociatedProducts) {
                        this.showError('All claim products cannot be in the Associated Product category. Please review and adjust accordingly.');
                        // this.message = 'All claim products cannot be in the Associated Product category. Please review and adjust accordingly';
                        // this.displayError = true;
                        // this.isLoading = false;
                        return false;
                    }

                    if(foundCPWithoutApprovalStatus) {
                        this.showError('Please ensure all claim items have been approved or rejected.');
                        // this.message = 'Please ensure all claim items have been approved or rejected.';
                        // this.displayError = true;
                        // this.isLoading = false;
                        return false;
                    }

                    this.message = '';
                    this.displayError = false;
                    // this.handleFinalSubmit();

                    this.closePreview();
                    // this.isLoading = true;
                    this.showProcessPanel = true;
                    this.handleFinalSubmit();
                    
                } else {

                    //VALID CLAIM LOGIC
                    let foundValidRecord = false;
                    console.log('this.cardData -> ' + JSON.stringify(this.cardData, null, 2));
                    let isProductFaultItemAvailableWithAssessmentLater = false;
                    let isAnyOtherItemAvailable = false;

                    this.cardData.forEach(element => {
                        if(element.readyForApproval != false &&
                            (
                                (element.selectedIssueSubCategory != '' && element.selectedIssueSubCategory != undefined) ||
                                (element.selectedOrderIssueSubCategory != '' && element.selectedOrderIssueSubCategory != undefined)
                            ) ||
                            element.selectedIssueCategory == 'Associated Product' ||
                            element.selectedIssueCategory == 'Freight Refund'
                        ) {
                            
                                foundValidRecord = true;
                            
                        }

                        //Auto-Approving items for warehouse profile
                        if(this.userProfileName == 'Warehouse') {
                            element.isApproved = true;
                            element.hasApprovalStatus = true;
                        }

                        if(element.selectedIssueCategory  == 'Product Issue' && element.selectedIssueSubCategory == 'Defective/Faulty Product' && (element.assesmentPossible == 'Later' || (element.assessmentTableData.length > 0 && element.assessmentTableData[0].Assessment_Outcome__c == 'Draft'))) {
                            isProductFaultItemAvailableWithAssessmentLater = true;
                        } else {
                            isAnyOtherItemAvailable = true;
                        }

                        // if(element.selectedIssueCategory == 'Order Issue' && element.selectedOrderIssueSubCategory == 'Duplicate Order' && element.proceedWithCancelOrder == 'No') {
                        //     foundValidRecord = false;
                        // }
                    });

                    this.cardData = JSON.parse(JSON.stringify(this.cardData));

                    console.log('foundValidRecord -> ' + JSON.stringify(foundValidRecord, null, 2));
                    if(foundValidRecord) {
                        //this.approvalScreen = true;
                    } else {
                        this.showNoProductErrorModal = true;
                        return false;
                    }

                    if(this.userProfileName == 'Warehouse' || (isProductFaultItemAvailableWithAssessmentLater == true && isAnyOtherItemAvailable == false)) {
                        this.approvalScreen = true;
                    } else {
                        this.showManagerCodeModal();
                    }
                }

            } else if(buttonName == 'editMode') {
                this.approvalScreen = false;
                this.currentItemIndex = 0;
                this.data = this.cardData[0];
            }

        } catch(error) {
            this.showError(error);
        }

    }

    showManagerCodeModal() {
        if(this.bypassApprovalForProfiles.includes(this.userProfileName)) {
            this.approvalScreen = true;
        } else {
            this.showManagerApprovalModel = true;
            this.managerCode = '';
        }

    }

    closeShowNoProduct() {
        this.showNoProductErrorModal = false;
    }


    handleInputChange(event) {
        this.managerCode = event.target.value;
    }

    handleManagerApproval() {
        if(!this.checkValidityForManagerApprovalScreen()){
            return;
        }
        this.isLoadingInModel = true;
        this.isPinError = false;
        this.pinFeedback = ''; // Clear previous feedback

        // Check if the manager code is 6 digits and numeric
        // if (!/^\d{6}$/.test(this.managerCode)) {
        //     this.isLoadingInModel = false;
        //     this.pinFeedback = 'Manager code must be a 6-digit number.';
        //     return;
        // }

        // Verify the manager pin
        verifyManagerPin({ pinstring: this.managerCode, managerId : this.selectedManagerFromList, manager: this.selectedManager })
            .then(result => {
                // Handle successful verification
                if (result === 'true') {
                    this.isLoadingInModel = false;
                    this.pinFeedback = 'PIN accepted. Creating case...'; // Show accepted feedback
                    // Call a method to create a case
                    // This method might be defined in your component or a parent component
                    this.closePreview();
                    // this.isLoading = true;
                    // this.showProcessPanel = true;
                    // this.handleFinalSubmit();
                    this.approvalScreen = true;
                    const scrollOption = {
                        left: 0,
                        top: 0,
                        behavior: 'smooth',
                        
                    }
                    window.scrollTo(scrollOption);
                }
            })
            .catch(error => {
                this.isLoading = false
                this.isPinError = true;
                // Handle errors
                this.isLoadingInModel = false;
                this.disableManagerSelection = true;
                this.pinFeedback = error.body.message; // Show error message from the server
            });
    }

    handleManagerApprovalBack() {
        this.isPinError = false;
        this.pinFeedback = '';
        this.managerCode = '';
        if(this.foundManagerFromApex == false) {
            this.selectedManager = undefined;
            this.disableManagerSelection = false;
        }
    }

    handleApproval(event) {
        let id =0;
        if(this.sfClaimProductActionID == undefined || this.sfClaimProductActionID == '') {
            id = event.target.dataset.id.substring(0, event.target.dataset.id.indexOf(';'));
        }
        let type = event.target.dataset.type;
        let cardData = JSON.parse(JSON.stringify(this.cardData));
        if(type == 'approve') {
            cardData[id].isApproved = true;
        } else {
            cardData[id].isApproved = false;
        }
        cardData[id].hasApprovalStatus = true;

        this.cardData = JSON.parse(JSON.stringify(cardData));
    }

    handleFinalSubmit() {
        // debugger;
        let pageName = 'outcome';
        let erpData = this.erpData;
        let jsonData = this.formatJSON();
        console.log('jsonData -> ', JSON.parse(JSON.stringify(jsonData)));
        console.log('this.isChangeResolution###'+this.isChangeResolution);
        this.showManagerApprovalModel = false;

        handleClaimSubmitted({ rawData: jsonData, caseId: this.sfCaseID, cpId: this.sfClaimProductID, cpaId: this.sfClaimProductActionID,isChangeResolution: this.isChangeResolution})
        .then(result => {
            let parsedResult = JSON.parse(result);
            this.assessmentIds = parsedResult.assessmentIds.length>0 ? parsedResult.assessmentIds : false;
            
            console.log('parsedResult -> ' + JSON.stringify(parsedResult, null, 2));
            console.log('this.processSteps - ' + this.processSteps);
            console.log('this.processSteps -> ' + JSON.stringify(this.processSteps, null, 2));

            let caseProcessStep = JSON.parse(JSON.stringify(this.processSteps[0]));
            if(this.sfClaimProductActionID) {
                caseProcessStep.label = 'Updating Claim (Case #' + parsedResult.caseNumber + ')';
            } else {
                caseProcessStep.label = 'Creating Claim (Case #' + parsedResult.caseNumber + ')';
            }
            caseProcessStep.showCopyToClipboard = true;
            caseProcessStep.recordnumber = parsedResult.caseNumber;
            this.processSteps[0] = caseProcessStep;

            if (this.userID) {
                let indexForNewCPA = 2;

                if(parsedResult.claimProducts != null) {
                    parsedResult.claimProducts.forEach(element => {
                        let eleObj = {
                            label: 'Claim Product #' + element.Name ,
                            isCompleted: true,
                            isProcessing: false,
                            isFailed: false,
                            isSkipped: false,
                            index : indexForNewCPA,
                            cpaId: element.Id,
                            cpaName: element.Name,
                            showErrorMessage: false, 
                            errorMessage: '',
                            showCopyToClipboard: false,
                            showLinkToTools: false,
                            showPrintReturn: true
                        }
                        if(element.Return_Pallet__c != undefined && element.Return_Pallet__c != '') {
                            eleObj.showIdentifictionLabel = false;
                        } else {
                            eleObj.showIdentifictionLabel = true;
                        }

                        this.processSteps.push(eleObj);
                        indexForNewCPA = indexForNewCPA+1;
                    })
                }

                let localProcessSteps = JSON.parse(JSON.stringify(this.processSteps));
                localProcessSteps.push({ label: "Retrieving Claim Product Action(s)", isCompleted: false, isProcessing: false, isFailed:false, isSkipped: false, index: indexForNewCPA, showErrorMessage: false, errorMessage: '', showCopyToClipboard: false, showLinkToTools: false, showPrintReturn: false});
                this.processSteps = JSON.parse(JSON.stringify(localProcessSteps));
                
                console.log('localProcessSteps -> ' , JSON.stringify(this.processSteps));
                console.log('localProcessSteps.length -> ' + this.processSteps.length);
                
                for (let index = 0; index < localProcessSteps.length-1; index++) {
                    this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);
                }
                
                console.log('this.currentProcessingStep -> ' + this.currentProcessingStep);
                
                this.processSteps.forEach(element => {
                    if(element.label == 'Retrieving Claim Product Action(s)') {
                        this.currentProcessingStep = element.index;
                    }
                });

                console.log('this.currentProcessingStep -> ' + this.currentProcessingStep);
                
                if(this.sfClaimProductActionID != undefined && this.sfClaimProductActionID != '') {
                    // this.closePage(this.sfClaimProductActionID);
                    this.fetchClaimProductActionsForCP();
                } else {
                    // this.closePage(parsedResult.caseID);
                    this.fetchClaimProductActions(parsedResult.caseID);
                }
            } else {
                this.sendNavigateEvent(pageName, erpData, parsedResult.caseNumber);
            }
            if(this.bypassScreen != true) {
                this.isLoading = false;
            }

        }).catch(error => {
            this.showProcessPanel = false;
            this.resetProcessingSteps();
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
            this.isLoading = false;

        });
    }

    fetchClaimProductActions(caseId) {
        this.newCaseId = caseId;

        // this.updateStepCompletion("Createing Case, Claim Product(s), Claim Product Action(s)", "Fetching Claim Product Action(s)");
        // this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);
        // this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);


        getClaimProductActionsFromCaseId({ caseId: caseId })
        .then(result => {
            let parsedResult = result;
            // this.updateStepCompletion("Fetching Claim Product Action(s)", "Queuing Claim Product Action(s)");
            //Completing the Queuing Claim Product Action(s) step
            // this.updateStepCompletionByIndex(this.currentProcessingStep, 'success');

            let cpaTostartProcessing = JSON.parse(JSON.stringify(this.currentProcessingStep));

            if(parsedResult != undefined && parsedResult != null && parsedResult.length > 0) {
                let indexForNewCPA = this.currentProcessingStep+1;
                
                parsedResult.forEach(element => {
                    let eleObj = {
                        label: "Processing "+ element.Action_Type__c + ' (' +element.Name + ')',
                        isCompleted: false,
                        isProcessing: false,
                        isFailed: false,
                        isSkipped: false,
                        index : indexForNewCPA,
                        cpaId: element.Id,
                        cpaName: element.Name,
                        showErrorMessage: false, 
                        errorMessage: '',
                        showCopyToClipboard: false,
                        showLinkToTools: false,
                        showPrintReturn: false
                    }

                    let skippedList = [ 'Email to call center'];
                    if(skippedList.includes(element.Action_Type__c)) {
                        // eleObj.isSkipped = true;
                        eleObj.isCompleted = true;
                    }
                    this.processSteps.push(eleObj);
                    indexForNewCPA = indexForNewCPA+1;
                })
                
                this.currentProcessingStep = cpaTostartProcessing;
                
                console.log('1255 cpaTostartProcessing - > ' + cpaTostartProcessing);   

                this.showCloseClaimButton = false;
                this.showFooterButtonsOnProcessingModal = false;
                
                
                // console.log('parsedResult ->', JSON.stringify(parsedResult));
                this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);

                console.log('this.currentProcessingStep -> ' + this.currentProcessingStep);

                if(this.processSteps[this.currentProcessingStep]){
                    let currentCPAForProcessing = this.processSteps[this.currentProcessingStep];
                    if(currentCPAForProcessing && currentCPAForProcessing.cpaId){
                        this.currentCPAId = currentCPAForProcessing.cpaId;
                         console.log('1307 this.currentCPAId ->', JSON.stringify(currentCPAForProcessing));
                    }
                } 
                
                
                
                this.showExecutePanel = true;
            } else {
                //this.closePage(caseId);
                this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);
                this.showFooterButtonsOnProcessingModal = true;
            }

        }).catch(error => {
            this.showProcessPanel = false;
            this.resetProcessingSteps();
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
            this.isLoading = false;

        });
    }

    fetchClaimProductActionsForCP() {
        this.newCaseId = this.sfCaseID;
        // this.updateStepCompletion("Createing Case, Claim Product(s), Claim Product Action(s)", "Fetching Claim Product Action(s)");
        // this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);
        // this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);


        getClaimProductActionsFromCPId({ cpId: this.sfClaimProductID, cpaId: this.sfClaimProductActionID })
        .then(result => {
            let parsedResult = result;
            // this.updateStepCompletion("Fetching Claim Product Action(s)", "Queuing Claim Product Action(s)");
            //Completing the Queuing Claim Product Action(s) step
            // this.updateStepCompletionByIndex(this.currentProcessingStep, 'success');


            if(parsedResult != undefined && parsedResult != null && parsedResult.length > 0) {
                let indexForNewCPA = this.currentProcessingStep+1;
                
                parsedResult.forEach(element => {
                    let eleObj = {
                        label: "Processing "+ element.Action_Type__c + ' (' +element.Name + ')',
                        isCompleted: false,
                        isProcessing: false,
                        isFailed: false,
                        isSkipped: false,
                        index : indexForNewCPA,
                        cpaId: element.Id,
                        cpaName: element.Name,
                        showErrorMessage: false, 
                        errorMessage: '',
                        showCopyToClipboard: false,
                        showLinkToTools: false
                    }

                    let skippedList = ['Warehouse Outcome for RTS', 'Email to call center'];
                    if(skippedList.includes(element.Action_Type__c)) {
                        // eleObj.isSkipped = true;asdfa 
                        eleObj.isCompleted = true;
                    }
                    this.processSteps.push(eleObj);
                    indexForNewCPA = indexForNewCPA+1;
                })
                
                // console.log('parsedResult ->', JSON.stringify(parsedResult));
                this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);
                if(this.processSteps[4]){
                    let currentCPAForProcessing = this.processSteps[4];
                    if(currentCPAForProcessing && currentCPAForProcessing.cpaId){
                        this.currentCPAId = currentCPAForProcessing.cpaId;
                       console.log('1379 this.currentCPAId -> ' + JSON.stringify(this.currentCPAId, null, 2));
                    }
                }
                this.showCloseClaimButton = false;
                this.showFooterButtonsOnProcessingModal = false;
    
               
                this.showExecutePanel = true;
            } else {
                //this.closePage(caseId);
                this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', '', false);
                this.showFooterButtonsOnProcessingModal = true;
            }

        }).catch(error => {
            this.showProcessPanel = false;
            this.resetProcessingSteps();
            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);
            this.isLoading = false;

        });
    }

    handleCPAProcessCompletion(event) {
        debugger;
        // this.showExecutePanel = false;
        console.log('in handleCPAProcessCompletion');
        try {
            if(event.detail.value.recordIds != undefined) {
                this.recordIds = event.detail.value.recordIds;
            }

            if(event.detail.value.status == 'success' || event.detail.value.status == 'Completed') {
                this.updateStepCompletionByIndex(this.currentProcessingStep, 'success', '', event.detail.value.orderId, false);
            } else {
                this.updateStepCompletionByIndex(this.currentProcessingStep, 'failed', event.detail.value.message, '', event.detail.value.skip);
            }
            
            // setTimeout(() => {
                if(this.currentProcessingStep == this.processSteps.length) {
                    // setTimeout(() => {
                    //     this.closePage(this.newCaseId);    
                    // }, 2000);
                    // debugger;
                    this.showCloseClaimButton = true;
                    this.showFooterButtonsOnProcessingModal = true;
    
                    this.processSteps.forEach(element => {
                        if(element.isFailed == true) {
                            this.showCloseClaimButton = false;
                        }
                    });
                } else {
                    if(this.processSteps[this.currentProcessingStep]){
                        let currentCPAForProcessing = this.processSteps[this.currentProcessingStep];
                        if(currentCPAForProcessing && currentCPAForProcessing.cpaId){
                            this.currentCPAId = currentCPAForProcessing.cpaId;
                            console.log('1432 this.currentCPAId ->', JSON.stringify(currentCPAForProcessing));
                        }
                    }
                    
                   
                    // this.showExecutePanel = true;    
                }
            // }, 1000);
        } catch (error) {
            console.log('error handleCPAProcessCompletion -> ');
        }
        
        
    }

    handleViewClaim() {
        this.closePage(this.newCaseId);
    }

    handleCloseClaim() {
        
        let value = {};

        let navigateChange = new CustomEvent('openserviceportal', {
            detail: { value },
            bubbles: true, //set to true to be able to access in grand parent
            composed: true, //set to true to be able to access in grand parent
        });

        this.dispatchEvent(navigateChange);

    }

    handleErrorMessageBox(event) {
        // debugger;
        // let indexVal = event.target.dataset.index;
        // let processStepsLocal = JSON.parse(JSON.stringify(this.processSteps));
        // processStepsLocal[indexVal].showErrorMessage = !processStepsLocal[indexVal].showErrorMessage;

        // this.processSteps = JSON.parse(JSON.stringify(processStepsLocal));
    }

    hadleCheckErrorCalled(event){
        
    }

    sleep(milliseconds) {
        const start = Date.now(); // Get the current timestamp
        let now = start;
        
        // Keep looping until the difference between now and start is greater than the provided milliseconds
        while (now - start < milliseconds) {
            now = Date.now(); // Update the current timestamp
        }
    }

    handleDialogClick(event) {

        let eventStatus = event.detail.value.status;

        if (eventStatus == 'confirm') {

            if (this.uploadedFiles.length > 0) {
                this.deleteFile(this.uploadedFiles, null, false);
            } else {

                let pageName = 'orderProducts';
                let erpData = this.erpData;

                this.modalLoading = false;
                this.disableModalButtons = {
                    'confirm': false,
                    'cancel': false
                };
                this.uploadedFiles = [];
                this.sendNavigateEvent(pageName, erpData, null);

            }

        } else {
            this.showDialog = false;
        }

    }

    /**************************** child event hadler methods */
    handleDataChange(event) {
        this.cardData[event.detail.index] = event.detail.data;
        this.cardData = JSON.parse(JSON.stringify(this.cardData));
        console.log('order item 1477 this.cardData -> ' + JSON.stringify(this.cardData, null, 2));
        
        this.prepareItemQuantityJSON(this.cardData);
    }

    /******************************************************* */

    prepareItemQuantityJSON(cardData){
        this.showExecutePanel = true;
        cardData.forEach(item => {
            if (item.addBackToStockItems != null  || item.addBackToStockItems != undefined) {
                item.addBackToStockItems.forEach(addBackItem => {
                    this.ProductQunatityDetailsParent.push({
                        sku: addBackItem.sku,
                        qty: addBackItem.qtyToOrder,
                      });
                });
              }
              if (this.ProductQunatityDetailsParent != null  || this.ProductQunatityDetailsParent != undefined) {

              this.ProductQunatityDetailsParentAfterDuplicates = [
                ...new Map(this.ProductQunatityDetailsParent.map(item => [item.sku, item])).values()
              ];
            }
          });
          if (this.ProductQunatityDetailsParentAfterDuplicates != null  || this.ProductQunatityDetailsParentAfterDuplicates != undefined) {
        console.log('ProductQunatityDetailsParentAfterDuplicates'+JSON.stringify(this.ProductQunatityDetailsParentAfterDuplicates));
          }
         
    }


    formatJSON() {

        console.log('inside formatJSON');

        let erpData = this.erpData;
        let orderProductList = this.orderProductList;
        let cardData = this.cardData;
        let generatedJSON = JSON.parse(JSON.stringify(this.generatedJSON));
        let jsonAcct = generatedJSON.account;
        let jsonOrder = generatedJSON.order;
        let jsonCase = generatedJSON.case;
        let jsonOrderItem = generatedJSON.orderItem;
        let jsonClaimProduct = generatedJSON.claimProduct;
        let currentdate = new Date();
        if(this.thirdScreenDetails == undefined) {
            this.thirdScreenDetails = {
                'commUserDetails': {}
            };
        }

        // Format Shipping and Billing Street
        let shipStreet = erpData.shipping_address.street ? erpData.shipping_address.street : '';
        let billStreet = erpData.billing_address.street ? erpData.billing_address.street : '';

        if (Array.isArray(shipStreet)) {
            shipStreet = shipStreet.join(", ");
        }

        shipStreet = ldsUtils.trimString(shipStreet, 0, 255);

        if (Array.isArray(billStreet)) {
            billStreet = billStreet.join(", ");
        }

        billStreet = ldsUtils.trimString(billStreet, 0, 255);

        // ACCOUNT

        if (erpData.order_type != 'Camper Trailers') {

            jsonAcct[ACCOUNT_FIRST_NAME_FIELD.fieldApiName] = erpData.customer.firstname ? erpData.customer.firstname : '';
            jsonAcct[ACCOUNT_LAST_NAME_FIELD.fieldApiName] = erpData.customer.lastname ? erpData.customer.lastname : '';
            jsonAcct[ACCOUNT_EMAIL_FIELD.fieldApiName] = erpData.customer.email ? erpData.customer.email : '';
            jsonAcct[ACCOUNT_CUSTOM_EMAIL_FIELD.fieldApiName] = erpData.customer.email ? erpData.customer.email : '';
            jsonAcct[ACCOUNT_PHONE_FIELD.fieldApiName] = erpData.customer.telephone ? erpData.customer.telephone : '';

        } else {
            jsonAcct[ACCOUNT_ID_FIELD.fieldApiName] = erpData.customer.acctID ? erpData.customer.acctID : '';
        }

        // CASE
        jsonCase[CASE_ACCOUNT_ID_FIELD.fieldApiName] = '';
        jsonCase[CASE_CONTACT_ID_FIELD.fieldApiName] = '';
        jsonCase[CASE_ORDER_ID_FIELD.fieldApiName] = '';
        jsonCase[CASE_CASE_OWNER_FIELD.fieldApiName] = this.caseOwnerID;
        jsonCase[CASE_SUBJECT_FIELD.fieldApiName] = erpData.order_number + ' - '  + erpData.customer.lastname + ', ' + erpData.customer.firstname;
        jsonCase[CASE_CASE_ORIGIN_FIELD.fieldApiName] = this.caseOrigin ? this.caseOrigin : 'Web';
        jsonCase[CASE_TYPE_FIELD.fieldApiName] = erpData.store_name == 'WHOLESALE ACCOUNTS' ? 'Commercial' :
                                                 erpData.store_name == '4WD SUPACENTRE (CAMPER TRAILERS)' || erpData.order_type == 'Camper Trailers' ?
                                                                       'Camper Trailers' : 'Claims Department';
        console.log('***** erpData.custCommCheckbox value:'+erpData.custCommCheckbox);
        jsonCase[CASE_TURN_OFF_CUSTOMER_COMMUNICATIONS_FIELD.fieldApiName] = erpData.custCommCheckbox;
        // jsonCase[CASE_REASON_FIELD.fieldApiName] = this.thirdScreenDetails.reasonValue;
        jsonCase[CASE_DESCRIPTION_FIELD.fieldApiName] = this.thirdScreenDetails.descriptionValue;
        jsonCase[CASE_SEND_NOTIFICATION_DATETIME_FIELD.fieldApiName] = currentdate;
        jsonCase[CASE_END_USER_ADDRESS_FIELD.fieldApiName] = this.thirdScreenDetails.commUserDetails.address;
        jsonCase[CASE_END_USER_EMAIL_FIELD.fieldApiName] = this.thirdScreenDetails.commUserDetails.email;
        jsonCase[CASE_END_USER_MOBILE_FIELD.fieldApiName] = this.thirdScreenDetails.commUserDetails.mobile;
        jsonCase[CASE_END_USER_NAME_FIELD.fieldApiName] = this.thirdScreenDetails.commUserDetails.name;
        jsonCase[CASE_END_USER_SUPPLIED_REPLACEMENT_FIELD.fieldApiName] = this.thirdScreenDetails.commUserDetails.suppliedReplace ?
                                                                          JSON.parse(this.thirdScreenDetails.commUserDetails.suppliedReplace) : false;
        if(this.sfReclaim == 'Yes') {
            jsonCase[CASE_PARENT_ID_FIELD.fieldApiName] = this.sfReclaimCaseId;
        }

        // ORDER
        if (erpData.order_type != 'Camper Trailers') {

            jsonOrder[ORDER_ORDER_NUMBER_FIELD.fieldApiName] = erpData.order_number ? erpData.order_number : '';
            jsonOrder[ORDER_ACCOUNT_ID_FIELD.fieldApiName] = '';
            jsonOrder[ORDER_ORDER_TYPE_FIELD.fieldApiName] = 'Original Order';
            jsonOrder[ORDER_CASE_ID_FIELD.fieldApiName] = '';
            jsonOrder[ORDER_STORE_NAME_FIELD.fieldApiName] = erpData.store_name ? erpData.store_name : '';
            jsonOrder[ORDER_ORDER_START_DATE_FIELD.fieldApiName] = erpData.order_date ? Date.parse(erpData.order_date.split(' ')[0]) : '';
            jsonOrder[ORDER_STATUS_FIELD.fieldApiName] = erpData.order_status ? erpData.order_status : 'processing';

            // ORDER (SHIPPING ADDRESS DETAILS)
            jsonOrder[ORDER_SHIPPING_FIRST_NAME_FIELD.fieldApiName] = erpData.shipping_address.firstname ? erpData.shipping_address.firstname : '';
            jsonOrder[ORDER_SHIPPING_LAST_NAME_FIELD.fieldApiName] = erpData.shipping_address.lastname ? erpData.shipping_address.lastname : '';
            jsonOrder[ORDER_SHIPPING_STATE_FIELD.fieldApiName] = erpData.shipping_address.state ? ldsUtils.trimString(erpData.shipping_address.state, 0, 80) : '';
            jsonOrder[ORDER_SHIPPING_POSTAL_CODE_FIELD.fieldApiName] = erpData.shipping_address.postcode ? ldsUtils.trimString(erpData.shipping_address.postcode, 0, 20) : '';
            jsonOrder[ORDER_SHIPPING_STREET_FIELD.fieldApiName] = shipStreet;
            jsonOrder[ORDER_SHIPPING_CITY_FIELD.fieldApiName] = erpData.shipping_address.suburb ? ldsUtils.trimString(erpData.shipping_address.suburb, 0, 40) : '';
            jsonOrder[ORDER_API_SHIPPING_CITY_FIELD.fieldApiName] = erpData.shipping_address.suburb ? erpData.shipping_address.suburb : '';
            jsonOrder[ORDER_SHIPPING_COUNTRY_FIELD.fieldApiName] = erpData.shipping_address.country_code ? ldsUtils.trimString(erpData.shipping_address.country_code, 0, 80) : '';
            jsonOrder[ORDER_SHIPPING_EMAIL_FIELD.fieldApiName] = erpData.shipping_address.email ? erpData.shipping_address.email : '';
            jsonOrder[ORDER_SHIPPING_TELEPHONE_FIELD.fieldApiName] = erpData.shipping_address.telephone ? erpData.shipping_address.telephone : '';

            // ORDER (BILLING ADDRESS DETAILS)
            jsonOrder[ORDER_BILLING_FIRST_NAME_FIELD.fieldApiName] = erpData.billing_address.firstname ? erpData.billing_address.firstname : '';
            jsonOrder[ORDER_BILLING_LAST_NAME_FIELD.fieldApiName] = erpData.billing_address.lastname ? erpData.billing_address.lastname : '';
            jsonOrder[ORDER_BILLING_STATE_FIELD.fieldApiName] = erpData.billing_address.state ? ldsUtils.trimString(erpData.billing_address.state, 0, 80) : '';
            jsonOrder[ORDER_BILLING_POSTAL_CODE_FIELD.fieldApiName] = erpData.billing_address.postcode ? ldsUtils.trimString(erpData.billing_address.postcode, 0, 20) : '';
            jsonOrder[ORDER_BILLING_STREET_FIELD.fieldApiName] = billStreet;
            jsonOrder[ORDER_BILLING_CITY_FIELD.fieldApiName] = erpData.billing_address.suburb ? ldsUtils.trimString(erpData.billing_address.suburb, 0, 40) : '';
            jsonOrder[ORDER_API_BILLING_CITY_FIELD.fieldApiName] = erpData.billing_address.suburb ? erpData.billing_address.suburb : '';
            jsonOrder[ORDER_BILLING_COUNTRY_FIELD.fieldApiName] = erpData.billing_address.country_code ? ldsUtils.trimString(erpData.billing_address.country_code, 0, 80) : '';
            jsonOrder[ORDER_BILLING_EMAIL_FIELD.fieldApiName] = erpData.billing_address.email ? erpData.billing_address.email : '';
            jsonOrder[ORDER_BILLING_TELEPHONE_FIELD.fieldApiName] = erpData.billing_address.telephone ? erpData.billing_address.telephone : '';

            // ORDER (ADDITIONAL DETAILS)
            jsonOrder[ORDER_ORDER_VALUE_FIELD.fieldApiName] = erpData.order_value ? erpData.order_value : '';
            jsonOrder[ORDER_SALES_CHANNEL_FIELD.fieldApiName] = erpData.sales_channel ? erpData.sales_channel : '';
            jsonOrder[ORDER_REFUND_TYPE_FIELD.fieldApiName] = erpData.refund_type ? erpData.refund_type : '';
            jsonOrder[ORDER_PAYMENT_METHOD_FIELD.fieldApiName] = erpData.payment_method ? erpData.payment_method : '';
            jsonOrder[ORDER_PAYMENT_REFERENCE_FIELD.fieldApiName] = erpData.payment_reference ? erpData.payment_reference : '';
            jsonOrder[ORDER_SHIPPING_AMOUNT_FIELD.fieldApiName] = erpData.shipping_amount ? erpData.shipping_amount : '';
            jsonOrder[ORDER_DESCRIPTION_FIELD.fieldApiName] = this.thirdScreenDetails.descriptionValue;

        } else {
            jsonOrder[ORDER_ID_FIELD.fieldApiName] = erpData.order_sf_id ? erpData.order_sf_id : '';
        }

        // ORDER PRODUCTS

        let orderItemsList = [];

        for (let i = 0; i < orderProductList.length; i++) {

            let qty = orderProductList[i].qty;
            let item = orderProductList[i].data;
            let orderItem = {};

            orderItem['attributes'] = jsonOrderItem.attributes;

            if (erpData.order_type != 'Camper Trailers') {

                orderItem[ORDER_PRODUCT_ITEM_ID_FIELD.fieldApiName] = item.item_id ? item.item_id : '';
                orderItem[ORDER_PRODUCT_PRODUCT_ID_FIELD.fieldApiName] = item.sku ? item.sku : '';
                orderItem[ORDER_PRODUCT_CASE_ID_FIELD.fieldApiName] = '';
                orderItem[ORDER_PRODUCT_DESCRIPTION_FIELD.fieldApiName] = this.thirdScreenDetails.descriptionValue;
                orderItem[ORDER_PRODUCT_ORDER_ID_FIELD.fieldApiName] = '';
                orderItem[ORDER_PRODUCT_QUANTITY_FIELD.fieldApiName] = qty ? qty : 0;
                orderItem[ORDER_PRODUCT_AVAILABLE_QUANTITY_TO_CLAIM_FIELD.fieldApiName] = item.qty ? item.qty : '';
                orderItem[ORDER_PRODUCT_QUANTITY_SHIPPED_FIELD.fieldApiName] = item.qty_shipped ? item.qty_shipped : '';
                orderItem[ORDER_PRODUCT_UNIT_PRICE_FIELD.fieldApiName] = item.item_price ? item.item_price : '';
                orderItem[ORDER_PRODUCT_ROW_TOTAL_PRICE_FIELD.fieldApiName] = item.row_total_price ? item.row_total_price : '';
                orderItem[ORDER_PRODUCT_COST_FIELD.fieldApiName] = item.item_cost ? item.item_cost : '';
                orderItem[ORDER_PRODUCT_SHIPPING_REFERENCE_FIELD.fieldApiName] = item.shipping_references != undefined && item.shipping_references != '[]' && item.shipping_references != [] && item.shipping_references != "[]" ? item.shipping_references.join(', ') : '';

            } else {
                orderItem[ORDER_PRODUCT_ID_FIELD.fieldApiName] = item.sf_order_item_id ? item.sf_order_item_id : '';
            }

            orderItem[ORDER_PRODUCT_JSON_ID_FIELD.fieldApiName] = orderProductList[i].uniqueID;

            orderItemsList.push(orderItem);

        }


        // CLAIM PRODUCTS

        let claimProductsList = [];
        let claimProductsActionList = [];
        let assessmentTableDataList = [];
        let uploadedFiles = [];

        // console.log('1640vcardData -> ' + JSON.stringify(cardData, null, 2));
        for (let i = 0; i < cardData.length; i++) {

            let item = cardData[i];
            console.log('item@@@@'+JSON.stringify(item));
            let claimProduct = {};
            let serialNumber = '';
            let batchNumber = '';

            //VALID CLAIM LOGIC
            if(item.readyForApproval == false) {
                continue;
            }

            let uploadedFilesForClaim = [];
            item.uploadedFiles.forEach(function(obj) {
                uploadedFiles.push(obj.fileID);
                uploadedFilesForClaim.push(obj.fileID);
            });

            claimProduct['attributes'] = jsonClaimProduct.attributes;
            claimProduct[CLAIM_PRODUCT_CASE_ID_FIELD.fieldApiName] = '';
            claimProduct[CLAIM_PRODUCT_ITEM_ID_FIELD.fieldApiName] = item.itemID;
            claimProduct[CLAIM_PRODUCT_ORDER_PRODUCT_ID_FIELD.fieldApiName] = item.uniqueID;
            claimProduct[CLAIM_PRODUCT_PRODUCT_ID_FIELD.fieldApiName] = item.sku;
            claimProduct[CLAIM_PRODUCT_BATCH_NUMBER_FIELD.fieldApiName] = item.batchValue != '' ? item.batchValue : null;
            claimProduct[CLAIM_PRODUCT_SERIAL_NUMBER_FIELD.fieldApiName] = item.serialValue != '' ? item.serialValue : null;
            claimProduct[RETURN_ORDER_TRACKING_NUMBER_FIELD.fieldApiName] = item.trackingNumber;
            claimProduct[CLAIM_PRODUCT_RESOLUTION_FIELD.fieldApiName] = item.selectedResolution;
            claimProduct[CLAIM_PRODUCT_IS_CHILD_ITEM_FIELD.fieldApiName] = item.isChild;
            claimProduct[CLAIM_PRODUCT_UPLOADED_FILES_ID_FIELD.fieldApiName] = uploadedFilesForClaim ? uploadedFilesForClaim.join(',') : '';
            claimProduct[CLAIM_PRODUCT_ITEM_COST_FIELD.fieldApiName] = item.itemCost;
            claimProduct[CLAIM_PRODUCT_ITEM_PRICE_FIELD.fieldApiName] = item.itemPrice;
            claimProduct[CLAIM_PRODUCT_ADVERTISED_PRICE_FIELD.fieldApiName] = item.priceAdvertised;
            claimProduct[CLAIM_NOTIFY_CUSTOMER_FIELD.fieldApiName] = true;
            claimProduct[CLAIM_PRODUCT_CLAIM_FORM_ID_FIELD.fieldApiName] = item.id;
            if(item.hasApprovalStatus != undefined && item.hasApprovalStatus != '' && item.hasApprovalStatus == true) {
                claimProduct[CLAIM_PRODUCT_APPROVAL_STATUS_FIELD.fieldApiName] = item.isApproved == true ? 'Approved' : 'Rejected';
                claimProduct[CLAIM_PRODUCT_APPROVED_BY_FIELD.fieldApiName] = this.selectedManager;
                claimProduct[CLAIM_PRODUCT_APPROVED_BY_PIN_NAME_FIELD.fieldApiName] = this.selectedManagerNameFromList;
                console.log('in 1918');
                let hasDraft = false;
                if(item && item.assessmentTableData && item.assessmentTableData.length > 0) {
                    for(let temp of item.assessmentTableData) {
                        if(temp.Assessment_Outcome__c == 'Draft') {
                            hasDraft =  true;
                        }
                    }
                }
                console.log('hasDraft',hasDraft);
            
                if((item.isPendingAssessment == true && item.assesmentPossible == 'Later') || hasDraft) {
                    claimProduct[CLAIM_PRODUCT_APPROVAL_STATUS_FIELD.fieldApiName] = 'Pending Assessment';
                }
            }

            if(this.bypassScreen == true) {
                claimProduct[CLAIM_PRODUCT_REASON_FIELD.fieldApiName] = 'RTS';
                claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
                claimProduct[CLAIM_PRODUCT_RETURNED_FIELD.fieldApiName] = 'Yes';
                claimProduct[CLAIM_PRODUCT_NEEDS_ASSESMENT_FIELD.fieldApiName] = 'No';
                claimProduct[CLAIM_PRODUCT_STATUS_FIELD.fieldApiName] = 'Returned';
                claimProduct[CLAIM_PRODUCT_RETURNED_DATE_TIME_FIELD.fieldApiName] = this.currentDateTime;
                claimProduct[CLAIM_PRODUCT_RETURNED_STAFF_MEMBER_NAME_FIELD.fieldApiName] = this.userName;
                claimProduct[CLAIM_PRODUCT_RETURNED_LOCATION_FIELD.fieldApiName] = this.returnedLocationValue;
                //claimProduct[CLAIM_PRODUCT_ADD_BACK_TO_STOCK_FIELD.fieldApiName] = true; It needs to be  flagged only on process action successful
                claimProduct[CLAIM_PRODUCT_RECEIVED_AT_DC_FIELD.fieldApiName] = this.returnedLocationValue;
                claimProduct[CLAIM_PRODUCT_DC_OUTCOME_FIELD.fieldApiName] = 'Added Back to Stock';
                claimProduct[CLAIM_PRODUCT_DC_OUTCOME_DATETIME_FIELD.fieldApiName] = new Date();
            }

            //NEW PROCESS MAPPING
            claimProduct[CLAIM_PRODUCT_IS_ADD_BACK_TO_STOCK_REQUIRED_FIELD.fieldApiName] = item.isAddbackToStockRequired;
            claimProduct[CLAIM_PRODUCT_ACL_SEVERITY_FIELD.fieldApiName] = 'N/A';
            claimProduct[CLAIM_PRODUCT_SHIPPING_STATUS_FIELD.fieldApiName] = item.shipmentStatus == 'Pending to Ship' ? 'No' : 'Yes';
            claimProduct[CLAIM_PRODUCT_REASON_FIELD.fieldApiName] = item.selectedIssueCategory;
            claimProduct[CLAIM_PRODUCT_REASON_SUB_CATEGORY_FIELD.fieldApiName] = item.selectedIssueCategory == 'Order Issue' ? item.selectedOrderIssueSubCategory : item.selectedIssueSubCategory;
            claimProduct[WHY_HAS_THE_PRODUCT_BEEN_RETURNED_FIELD.fieldApiName] = item.whyHasTheProductBeenReturned;
            claimProduct[PRODUCT_RTS_OTHER_DESCRIPTION_FIELD.fieldApiName] = item.otherReturnReason;

            claimProduct[CLAIM_PRODUCT_QUANTITY_RECEIVED_FIELD.fieldApiName] = item.CP_Quantity_Dispatched__c != undefined ? Number(item.CP_Quantity_Dispatched__c) : null;
            claimProduct[CLAIM_PRODUCT_QUANTITY_DIFF_FIELD.fieldApiName] = item.CP_Quantity_Difference__c != undefined ? Number(item.CP_Quantity_Difference__c) : null;

            claimProduct[CLAIM_PRODUCT_RESOLUTION_FIELD.fieldApiName] = this.preferredResolutionPicklistMapping[item.resolutionOutcome];
           
            //claimProduct[CLAIM_PRODUCT_RESOLUTION_ACTION_FIELD.fieldApiName] = this.resolutionActionPicklistMapping[item.resolutionOutcome];
            if (item.isApproved === true || ( item.isPendingAssessment == true && item.assesmentPossible == 'Later')) {
               claimProduct[CLAIM_PRODUCT_RESOLUTION_ACTION_FIELD.fieldApiName] = 
                   this.resolutionActionPicklistMapping[item.resolutionOutcome];
            } else {
                claimProduct[CLAIM_PRODUCT_RESOLUTION_ACTION_FIELD.fieldApiName] = 'rejected';
            }


            if(item.selectedIssueCategory == 'Product Issue' && item.isIssueResolved == 'No') {
                claimProduct[CLAIM_PRODUCT_DID_THIS_FAULTY_PRODUCT_CAUSE_DAMAGE_FIELD.fieldApiName] = item.didCauseDamage;
                claimProduct[CLAIM_PRODUCT_NEEDS_ASSESMENT_FIELD.fieldApiName] = 'Yes';
                if(item.assesmentPossible != undefined && item.assesmentPossible == 'Now') {

                    if(item.Return_Pallet__c != undefined && item.Return_Pallet__c != '') {
                        claimProduct[CLAIM_PRODUCT_RETURN_PALLET_FIELD.fieldApiName] = item.Return_Pallet__c;
                    }

                    claimProduct[CLAIM_PRODUCT_ASSESSMENT_STAFF_NAME_FIELD.fieldApiName] = item.assessmentStaffName;            
                    claimProduct[CLAIM_PRODUCT_RETURNED_STAFF_MEMBER_NAME_FIELD.fieldApiName] = item.assessmentStaffName;
                    claimProduct[CLAIM_PRODUCT_ASSESSMENT_OUTCOME_FIELD.fieldApiName] = item.assessmentOutcome;
                    claimProduct[CLAIM_PRODUCT_ASSESSMENT_FAULT_DESCRIPTION_FIELD.fieldApiName] = item.assessmentFaultDescription;
                    // if(item.assessmentFaultCategory == 'Other') {
                    //     claimProduct[CLAIM_PRODUCT_ASSESSMENT_FAULT_CATEGORY.fieldApiName] = item.assessmentFaultCategoryOther;
                    // } else {
                        claimProduct[CLAIM_PRODUCT_ASSESSMENT_FAULT_CATEGORY.fieldApiName] = item.assessmentFaultCategory;
                    // }

                    claimProduct[CLAIM_PRODUCT_ASSESSMENT_STATUS_FIELD.fieldApiName] = 'Completed';

                    // if(item.assessmentOutcome === 'Genuine Fault') {
                        claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = item.claimProductItemLocation;
                        if(item.claimProductItemLocation == undefined || item.claimProductItemLocation == '') {
                            if(this.userProfileName == 'Warehouse') {
                                claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'At DC - Awaiting Outcome';
                            } else {
                                if(item.userLocationName != undefined && item.userLocationName.includes(' DC')) {
                                    claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'At DC - Awaiting Outcome';
                                } else {
                                    claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'At Store';
                                    claimProduct[CLAIM_PRODUCT_RECEIVED_AT_STORE_DATETIME.fieldApiName] = new Date();
                                }
                            }
                        }
                        if(item.claimProductItemLocation == 'At Store') {
                            claimProduct[CLAIM_PRODUCT_RECEIVED_AT_STORE_DATETIME.fieldApiName] = new Date();
                        } else if(item.claimProductItemLocation == 'In Transit to DC') {
                            claimProduct[CLAIM_PRODUCT_TRANSIT_TO_DC_DATETIME.fieldApiName] = new Date();                    
                        } else if(item.claimProductItemLocation == 'At DC') {
                            claimProduct[CLAIM_PRODUCT_RETURNED_TO_DC_DATETIME.fieldApiName] = new Date();
                            claimProduct[CLAIM_PRODUCT_RETURNED_FIELD.fieldApiName] = 'Yes';
                        }
                        claimProduct[CLAIM_PRODUCT_DC_OUTCOME_FIELD.fieldApiName] = this.getDCOutcomeValue(item.claimProductFinalOutcome);
                        // claimProduct[CLAIM_PRODUCT_DC_OUTCOME_DATETIME_FIELD.fieldApiName] = new Date();
                    // }
                    // debugger;
                    if(item.shippingLocationName != '' && item.shippingLocationName != undefined && (this.sfClaimProductActionID == undefined || this.sfClaimProductActionID == '') && 
                        (item.resolutionOutcome != 'Replacement Item' && item.resolutionOutcome != 'Replacement Part')) {
                        
                        if(item.shippingLocationName == 'Standard Shipping') {
                            let shippingString = '';
                            shippingString += item.shipToAddress.street[0] + ' ' + item.shipToAddress.suburb + ' ';
                            shippingString += item.shipToAddress.state + ' ' + item.shipToAddress.postcode + ' ' + item.shipToAddress.country_code;
                            claimProduct[CLAIM_PRODUCT_SHIPPING_TO_ADDRESS_FIELD.fieldApiName] = shippingString;
                        } else {
                            // claimProduct[CLAIM_PRODUCT_SHIPPING_TO_ADDRESS_FIELD.fieldApiName] = item.shippingLocationName;
                        }

                    }

                    if(item.claimProductFinalOutcome == 'Product Disposed') {
                        const date = new Date();

                        let day = date.getDate();
                        let month = date.getMonth() + 1;
                        let year = date.getFullYear();
                        claimProduct[CLAIM_PRODUCT_CREATED_RETURN_DATE_FIELD.fieldApiName] = year+'-'+month+'-'+day;
                        claimProduct[CLAIM_PRODUCT_RETURNED_FIELD.fieldApiName] = 'Yes';
                        if(item.warrantyClaimDCDisposalCompulsory != true) {
                            claimProduct[CLAIM_PRODUCT_RETURNED_LOCATION_FIELD.fieldApiName] = (item.userLocationName != undefined && item.userLocationName != '') ? item.userLocationName : item.userLocation;
                        }
                    }
                    
                    let failedOutcomes = ['Faulty', 'Goodwill'];
                    if(failedOutcomes.includes(item.assessmentOutcome) && this.userProfileName == 'Store User') {
                        const date = new Date();

                        let day = date.getDate();
                        let month = date.getMonth() + 1;
                        let year = date.getFullYear();
                        claimProduct[CLAIM_PRODUCT_CREATED_RETURN_DATE_FIELD.fieldApiName] = year+'-'+month+'-'+day;
                        claimProduct[CLAIM_PRODUCT_RETURNED_FIELD.fieldApiName] = 'Yes';
                        if(item.warrantyClaimDCDisposalCompulsory != true) {
                            claimProduct[CLAIM_PRODUCT_RETURNED_LOCATION_FIELD.fieldApiName] = (item.userLocationName != undefined && item.userLocationName != '') ? item.userLocationName : item.userLocation;
                        }
                    }

                } else if(item.assesmentPossible != undefined && item.assesmentPossible == 'Later') {
                    claimProduct[CLAIM_PRODUCT_ASSESSMENT_STATUS_FIELD.fieldApiName] = 'Not Started';
                }
                let hasDraft = false;
                if(item && item.assessmentTableData && item.assessmentTableData.length > 0) {
                    for(let temp of item.assessmentTableData) {
                        if(temp.Assessment_Outcome__c == 'Draft') {
                            hasDraft =  true;
                        }
                    }
                }
                if(hasDraft) {
                    claimProduct[CLAIM_PRODUCT_ASSESSMENT_STATUS_FIELD.fieldApiName] = 'In Progress';
                }
            } else if(item.selectedIssueCategory == 'Associated Product') {
                claimProduct[CLAIM_PRODUCT_NEEDS_ASSESMENT_FIELD.fieldApiName] = 'No';
            }

            if(item.selectedIssueSubCategory == 'Other') {
                claimProduct[CLAIM_PRODUCT_ISSUE_DESCRIPTION_FIELD.fieldApiName] = item.Reason;
            } else {
                claimProduct[CLAIM_PRODUCT_ISSUE_DESCRIPTION_FIELD.fieldApiName] = item.issueDescription;
            }

            if(item.addBackToStockAction != undefined) {
                if(item.addBackToStockAction == 'Now' && item.addBackToStockActionLocation != undefined) {
                    claimProduct[CLAIM_PRODUCT_ADD_BACK_TO_STOCK_LOCATION_FIELD.fieldApiName] = item.userLocationName;
                    //claimProduct[CLAIM_PRODUCT_ADD_BACK_TO_STOCK_FIELD.fieldApiName] = true; It needs to be  flagged only on process action successful
                    //auto populates based on add back to stock
                    claimProduct[CLAIM_PRODUCT_RETURNED_LOCATION_FIELD.fieldApiName] = item.userLocationName;
                    if(item.userLocationName != undefined && item.userLocationName.includes(' DC')) {
                        claimProduct[CLAIM_PRODUCT_RECEIVED_AT_DC_FIELD.fieldApiName] = item.userLocationName;
                        claimProduct[CLAIM_PRODUCT_RETURNED_TO_DC_DATETIME.fieldApiName] = new Date();
                    }
                    claimProduct[CLAIM_PRODUCT_RETURNED_FIELD.fieldApiName] = '';
                    if(this.userProfileName == 'Warehouse') {
                        claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'At DC - Awaiting Outcome';
                    } else {
                        claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'At Store';
                        claimProduct[CLAIM_PRODUCT_RECEIVED_AT_STORE_DATETIME.fieldApiName] = new Date();

                    }

                    if(item.userLocationName != undefined && item.userLocationName.includes(' DC')) {
                        claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'At DC - Awaiting Outcome';
                    } else {
                        claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'At Store';
                        claimProduct[CLAIM_PRODUCT_RECEIVED_AT_STORE_DATETIME.fieldApiName] = new Date();
                    }

                    if(item.isBrandNewCondition != undefined && item.isBrandNewCondition == 'Yes') {
                        claimProduct[CLAIM_PRODUCT_DC_OUTCOME_FIELD.fieldApiName] = 'Added Back to Stock';
                        claimProduct[CLAIM_PRODUCT_DC_OUTCOME_DATETIME_FIELD.fieldApiName] = new Date();
                    } else if(item.isBrandNewCondition != undefined && item.isBrandNewCondition == 'No') {
                        claimProduct[CLAIM_PRODUCT_DC_OUTCOME_FIELD.fieldApiName] = 'Seconds';
                        claimProduct[CLAIM_PRODUCT_DC_OUTCOME_DATETIME_FIELD.fieldApiName] = new Date();
                    }
                    // claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'In transit to DC';
                } else if(item.addBackToStockAction == 'Create Return' || item.productReturnAction == 'Yes') {
                    if(item.selectedIssueSubCategory != 'Part Missing' && item.selectedIssueSubCategory != 'Lost in Transit') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    }
                    claimProduct[CLAIM_PRODUCT_CP_ITEM_LOCATION.fieldApiName] = 'In transit to DC';
                    claimProduct[CLAIM_PRODUCT_TRANSIT_TO_DC_DATETIME.fieldApiName] = new Date();
                    console.log('claimProduct[CLAIM_PRODUCT_TRANSIT_TO_DC_DATETIME.fieldApiName] -> ' + claimProduct[CLAIM_PRODUCT_TRANSIT_TO_DC_DATETIME.fieldApiName]);
                }
                if(item.returnMethod != undefined && item.returnMethod != '') {
                    claimProduct[CLAIM_PRODUCT_RETURN_METHOD_FIELD.fieldApiName] = item.returnMethod;
                    claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    if(item.trackingNumber != undefined) {
                        claimProduct[CLAIM_PRODUCT_RETURN_CONNOTE_NUMBER_FIELD.fieldApiName] = item.trackingNumber;
                    }
                    // debugger;
                    if((this.sfClaimProductActionID == undefined || this.sfClaimProductActionID == '')) {
                        // claimProduct[CLAIM_PRODUCT_SHIPPING_TO_ADDRESS_FIELD.fieldApiName] = item.shippingToName;
                        if(item.shippingLocationName == 'Standard Shipping' ) {
                            let shippingString = '';
                            shippingString += item.shipToAddress.street[0] + ' ' + item.shipToAddress.suburb + ' ';
                            shippingString += item.shipToAddress.state + ' ' + item.shipToAddress.postcode + ' ' + item.shipToAddress.country_code;
                            claimProduct[CLAIM_PRODUCT_SHIPPING_TO_ADDRESS_FIELD.fieldApiName] = shippingString;
                        } else {
                            // claimProduct[CLAIM_PRODUCT_SHIPPING_TO_ADDRESS_FIELD.fieldApiName] = item.shippingToName;
                        }
                        
                    }
                    if(item.courierName != undefined) {
                        claimProduct[CLAIM_PRODUCT_RETURN_COURIER_NAME_FIELD.fieldApiName] = item.courierName;
                    }
                }
            }

            if(item.dcReturn && item.dcReturn.returnMethod != undefined) {
                claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                claimProduct[CLAIM_PRODUCT_RETURN_METHOD_FIELD.fieldApiName] =  item.dcReturn?.returnMethod;
                if(item.dcReturn.trackingNumber != undefined) {
                    claimProduct[CLAIM_PRODUCT_RETURN_CONNOTE_NUMBER_FIELD.fieldApiName] = item.dcReturn?.trackingNumber;
                }
                if(item.dcReturn.courierName != undefined) {
                    claimProduct[CLAIM_PRODUCT_RETURN_COURIER_NAME_FIELD.fieldApiName] = item.dcReturn?.courierName;
                }
            }

            if(item.selectedIssueCategory == 'Transit/Delivery Issue' && this.data.selectedIssueSubCategory == 'Shipping') {
                claimProduct[CLAIM_PRODUCT_FREIGHT_PAID_FIELD.fieldApiName] = Number(item.freightPaid);
                claimProduct[CLAIM_PRODUCT_FREIGHT_REFUNDED_FIELD.fieldApiName] = Number(item.freightRefunded);
            }

            if(item.selectedIssueSubCategory == 'Lost in Transit') {
                claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
            }

            if(item.didCauseDamage == 'Yes') {
                jsonCase[CASE_TYPE_FIELD.fieldApiName] = 'Claims Department (All Open Escalations)';
            }else{
                this.assignCaseTypeBasedOnLocation();
               
                jsonCase[CASE_TYPE_FIELD.fieldApiName] = this.caseTypeToAssign;
            }

            if(item.CP_Incorrecte_SKU_Id__c != undefined) {
                claimProduct[CLAIM_PRODUCT_INCORRECT_SKU_FIELD.fieldApiName] = item.CP_Incorrecte_SKU_Id__c;
            }
            
            //Must return check
            if(item.shipmentStatus == 'Pending to Ship') {
                claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
            } {
                if (item.selectedIssueCategory == '30 Day Change of Mind' ) {
                    if(item.selectedIssueSubCategory == 'Does not suit customer needs') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    } else if(item.selectedIssueSubCategory == 'Other') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    }
                } else if (item.selectedIssueCategory == 'Product Issue') {
                    if(item.selectedIssueSubCategory == 'Defective/Faulty Product') {
                        if(item.assesmentPossible == 'Now' && item.claimProductFinalOutcome == 'Product Disposed' && this.sfClaimProductActionID == undefined) {
                            claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
                        } else if(item.assesmentPossible == 'Now' && item.claimProductFinalOutcome != 'Product Disposed' && item.claimProductFinalOutcome != 'Returned to Customer'){
                            claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                        }
                    } else if(item.selectedIssueSubCategory == 'Parts Missing') { 
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
                    }
                } else if (item.selectedIssueCategory == 'Transit/Delivery Issue') {
                    if(item.selectedIssueSubCategory == 'Lost in transit') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
                    } else if(item.selectedIssueSubCategory == 'Return to Sender') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    } else if(item.selectedIssueSubCategory == 'Wrong Product Dispatched/Received') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    } else if(item.selectedIssueSubCategory == 'Wrong Product Quantity Dispatched/Received') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
                    }
                } else if (item.selectedIssueCategory == 'Order Issue') {
                    if(item.selectedOrderIssueSubCategory == 'Advertised at a lower price') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
                    } else if(item.selectedOrderIssueSubCategory == 'Product Not As Described') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    } else if(item.selectedOrderIssueSubCategory == 'Duplicate Order') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    } else if(item.selectedOrderIssueSubCategory == 'Pre-Order Delays') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
                    } else if(item.selectedOrderIssueSubCategory == 'Orderded Wrong Product') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                    } else if(item.selectedOrderIssueSubCategory == 'Cancel Order') {
                        claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
                    }
                } else if (item.selectedIssueCategory == 'Associated Product') {
                    claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'Yes';
                }
            }
            

            if(claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] == 'Yes') {
                claimProduct[CLAIM_PRODUCT_STATUS_FIELD.fieldApiName] = 'Awaiting Return';
            } else if(claimProduct[CLAIM_PRODUCT_NEEDS_ASSESMENT_FIELD.fieldApiName] == 'Yes') {
                if(claimProduct[CLAIM_PRODUCT_ASSESSMENT_FAULT_CATEGORY.fieldApiName] == undefined) {
                    claimProduct[CLAIM_PRODUCT_STATUS_FIELD.fieldApiName] = 'Awaiting Assessment';
                } else {
                    claimProduct[CLAIM_PRODUCT_STATUS_FIELD.fieldApiName] = 'Assessment Completed';
                }
            }

            

            if(claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] == undefined || claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] != 'Yes') {
                claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
            }

            if(claimProduct[CLAIM_PRODUCT_STATUS_FIELD.fieldApiName] == undefined || claimProduct[CLAIM_PRODUCT_STATUS_FIELD.fieldApiName] == '') {
                claimProduct[CLAIM_PRODUCT_STATUS_FIELD.fieldApiName] = 'Processing';
            }

            if(claimProduct[CLAIM_PRODUCT_APPROVAL_STATUS_FIELD.fieldApiName] != undefined && claimProduct[CLAIM_PRODUCT_APPROVAL_STATUS_FIELD.fieldApiName] == 'Rejected') {
                claimProduct[CLAIM_PRODUCT_STATUS_FIELD.fieldApiName] = 'Closed';
            }
            
            let itemCopy = JSON.parse(JSON.stringify(item));
            itemCopy.claimProductActions = [];
            claimProduct[CLAIM_PRODUCT_JSON_FIELD.fieldApiName] = JSON.stringify(itemCopy);
            claimProductsList.push(claimProduct);

            let hasReturnOrderCPA = false;

            if(item.claimProductActions != undefined && item.claimProductActions != []) {
                console.log('claimProductActions@@@@@'+this.claimProductActions);
                for (let j = 0; j < item.claimProductActions.length; j++) {
                    let claimProductActionItem = item.claimProductActions[j];
                    console.log('claimProductActionItem@@@'+JSON.stringify(claimProductActionItem));
                    let claimProductAction = {};
                    claimProductAction['attributes'] = {
                        "type": "Claim_Product_Action__c"
                    };
                    claimProductAction[CLAIM_PRODUCT_ACTION_ACTION_TYPE_FIELD.fieldApiName] = claimProductActionItem[CLAIM_PRODUCT_ACTION_ACTION_TYPE_FIELD.fieldApiName];
                    claimProductAction[CLAIM_PRODUCT_ACTION_CLAIM_PRODUCT_FIELD.fieldApiName] = item.id;
                    claimProductAction[CLAIM_PRODUCT_ACTION_JSON_FIELD.fieldApiName] = JSON.stringify(claimProductActionItem[CLAIM_PRODUCT_ACTION_JSON_FIELD.fieldApiName]);
                    claimProductAction[CLAIM_PRODUCT_ACTION_STATUS_FIELD.fieldApiName] = claimProductActionItem[CLAIM_PRODUCT_ACTION_STATUS_FIELD.fieldApiName];
                    if(item.hasApprovalStatus != undefined && item.hasApprovalStatus != '' && item.hasApprovalStatus == true) {
                        claimProductAction[CLAIM_PRODUCT_ACTION_APPROVED_BY_DATETIME_FIELD.fieldApiName] = new Date();
                        claimProductAction[CLAIM_PRODUCT_ACTION_APPROVED_BY_STAFF_MEMBER_FIELD.fieldApiName] = this.userId;
                    }
                    claimProductAction[CLAIM_PRODUCT_ACTION_AMOUNT_FIELD.fieldApiName] = claimProductActionItem[CLAIM_PRODUCT_ACTION_AMOUNT_FIELD.fieldApiName];
                    claimProductAction[CLAIM_PRODUCT_ACTION_DESCRIPTION_FIELD.fieldApiName] = claimProductActionItem[CLAIM_PRODUCT_ACTION_DESCRIPTION_FIELD.fieldApiName];
                    
                    if(claimProduct[CLAIM_PRODUCT_APPROVAL_STATUS_FIELD.fieldApiName] != undefined && claimProduct[CLAIM_PRODUCT_APPROVAL_STATUS_FIELD.fieldApiName] == 'Rejected') {
                        claimProductAction[CLAIM_PRODUCT_ACTION_STATUS_FIELD.fieldApiName] = 'Cancelled';
                    }

                    if(claimProductAction[CLAIM_PRODUCT_ACTION_ACTION_TYPE_FIELD.fieldApiName] == 'Create DC Return' || claimProductAction[CLAIM_PRODUCT_ACTION_ACTION_TYPE_FIELD.fieldApiName] == 'Return Order' || claimProductAction[CLAIM_PRODUCT_ACTION_ACTION_TYPE_FIELD.fieldApiName] == 'Add Back To Stock' || claimProductAction[CLAIM_PRODUCT_ACTION_ACTION_TYPE_FIELD.fieldApiName] == 'Add Back To Stock (Seconds)' || claimProductAction[CLAIM_PRODUCT_ACTION_ACTION_TYPE_FIELD.fieldApiName] == 'Add Back To Stock (Parts)' ) {
                        hasReturnOrderCPA = true;
                    }
                    
                    claimProductsActionList.push(claimProductAction);
                }
            }

            if(hasReturnOrderCPA != true) {
                claimProduct[CLAIM_PRODUCT_MUST_RETURN_FIELD.fieldApiName] = 'No';
            }


            if(item.assessmentTableData != undefined && item.assessmentTableData != []) {
                for (let j = 0; j < item.assessmentTableData.length; j++) {
                    console.log('item.assessmentTableData[j]@@@@@'+JSON.stringify(item.assessmentTableData[j]));
                    let assessmentTableDataItem = item.assessmentTableData[j];
                    assessmentTableDataItem['UUID__c'] = assessmentTableDataItem['uuid'];
                    delete assessmentTableDataItem['uuid'];
                    assessmentTableDataItem['id'] = item.id;
                    assessmentTableDataItem['attributes'] = {
                        "type": "Assessment_Form__c"
                    };
                    if(assessmentTableDataItem['Purchase_Date__c']){
                        assessmentTableDataItem['Purchase_Date__c'] =  this.formatToSalesforceDate(assessmentTableDataItem['Purchase_Date__c']);
                    }
                    if(assessmentTableDataItem['Purchase_Date__c']){
                        assessmentTableDataItem['Purchase_Date__c'] =  this.formatToSalesforceDate(assessmentTableDataItem['Purchase_Date__c']);
                    }if(assessmentTableDataItem['AC_DC_Charge_Start_Time_Date__c']){
                        assessmentTableDataItem['AC_DC_Charge_Start_Time_Date__c'] =  this.formatToSalesforceDate(assessmentTableDataItem['AC_DC_Charge_Start_Time_Date__c']);
                    }if(assessmentTableDataItem['AC_DC_Charge_Finish_Time_Date__c']){
                        assessmentTableDataItem['AC_DC_Charge_Finish_Time_Date__c'] =  this.formatToSalesforceDate(assessmentTableDataItem['AC_DC_Charge_Finish_Time_Date__c']);
                    }if(assessmentTableDataItem['Load_Test_Start_Time_Date__c']){
                        assessmentTableDataItem['Load_Test_Start_Time_Date__c'] =  this.formatToSalesforceDate(assessmentTableDataItem['Load_Test_Start_Time_Date__c']);
                    }if(assessmentTableDataItem['Load_Test_Finish_Time_Date__c']){
                        assessmentTableDataItem['Load_Test_Finish_Time_Date__c'] =  this.formatToSalesforceDate(assessmentTableDataItem['Load_Test_Finish_Time_Date__c']);
                    }
                    assessmentTableDataList.push(assessmentTableDataItem);
                   
                }
            }
        }


        let jsonData = {
            'orderType': erpData.order_type ? erpData.order_type : '',
            'accountDetails': jsonAcct,
            'orderDetails': jsonOrder,
            'caseDetails': jsonCase,
            'orderItemList': orderItemsList,
            'claimProductList': claimProductsList,
            'claimProductActionList': claimProductsActionList,
            'assessmentTableDataList': assessmentTableDataList,
            'cvIDList': uploadedFiles
        };


        return JSON.stringify(jsonData);

    }

    getDCOutcomeValue(cpFinalOucome) {
        let dcOutcomeMapping = new Map([
            ["Add Back To Stock (Seconds)", "Seconds"],
            ["Add Back To Stock (Parts)", "Parts"],
            ["Product Disposed", "Bin"],
            ["Returned to Customer", "Returned to Customer"]
          ]);
        return dcOutcomeMapping.get(cpFinalOucome);
    }

    formatToSalesforceDate(dateString) {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    validateSerialBatchValues(item) {
        if(item.serialRequired && (item.serialValue == 'N/A' || item.serialValue == '' || item.serialValue == undefined || item.serialValue == null)) {
            return false;
        }
        if(item.batchRequired && (item.batchValue == 'N/A' || item.batchValue == '' || item.batchValue == undefined || item.serialValue == null)) {
            return false;
        }
        return true;
    }

    handleManagerSelection(event) {
        this.selectedManager = event.detail.id;
    }

    handleStoreManagerSelectionPicklist(event) {
        this.selectedManagerFromList = event.detail.value;
        this.selectedManagerNameFromList = event.target.options.find(opt => opt.value === event.detail.value).label;
    }

    handleManagerSelectionPicklist(event) {
        this.selectedManager =  event.detail.value;
        this.selectedManagerFromList = '';
        this.selectedManagerNameFromList = '';
        this.getStoreManagerListFromApex();
    }

    /*************************** utility methods ************ */

    sendNavigateEvent(pageName, data, caseNumber) {

        let value = {
          'pageName': pageName,
          'erpData': data,
          'thirdScreenDetails': this.thirdScreenDetails,
          'caseNumber': caseNumber
        };

        let navigateChange = new CustomEvent('navigatepage', {
            detail: { value }
        });

        this.dispatchEvent(navigateChange);

    }

    closePreview() {
        this.showManagerApprovalModel = false;
        this.isPinError = false;
        this.pinFeedback = '';
        this.managerCode = '';
    }

    closeProcessingTimelinePreview() {
        this.showProcessPanel = false;
        this.resetProcessingSteps();
    }

    handlePrintReturnLabel(event) {
        this.showPrintReturnLabelWindow = true;
        this.printRecordId = event.target.dataset.recordid;
    }

    handlePrintIdentificationLabel(event) {
        this.showPrintIdentificationLabelWindow = true;
        this.printRecordId = event.target.dataset.recordid;
        if(!this.identificationPrinted.includes(this.printRecordId)){
            this.identificationPrinted.push(this.printRecordId);
            let tempStepss = [...this.processSteps];
            this.processSteps = [];
            this.processSteps = [...tempStepss];
        }
    }

    closePrintReturnLabelWindow() {
        this.showPrintReturnLabelWindow = false;
        this.printRecordId = null;
    }

    closePrintIdentificationLabelWindow() {
        this.showPrintIdentificationLabelWindow = false;
        this.printRecordId = null;
    }

    validateInput() {
        let valid = [...this.template.querySelectorAll("lightning-input"), ...this.template.querySelectorAll("lightning-combobox")].reduce(
            (validSoFar, input) => {
                if(input.checkValidity()) {
                    input.classList.add("slds-has-error");
                } else {
                    input.classList.remove("slds-has-error");
                }

                input.reportValidity();
                return validSoFar && input.checkValidity();
            },
            true
        )
        //Check for child
        let validChild = true;
        let childComp = this.template.querySelector('c-claim-form-cp-item');
        if(childComp) {
            validChild = childComp.validateInput();
        }

        return valid && validChild;
    }

    checkValidityForManagerApprovalScreen() {
        let isValid = true;

        // Reset error state
        this.isPinError = false;
        this.pinFeedback = '';

        // Check if manager selection is empty
        if (!this.selectedManager) {
            this.isPinError = true;
            this.pinFeedback = 'Manager selection is required.';
            isValid = false;
        }

        // Validate manager code
        if (this.selectedManager && (this.managerCode == null || this.managerCode == '' || !this.managerCode.match(/^\d{6}$/))) {
            this.isPinError = true;
            this.pinFeedback = 'Manager code must be 6 digits.';
            isValid = false;
        }

        if(!this.isValidOption(this.selectedManager)) {
            this.isPinError = true;
            this.pinFeedback = 'Manager selection is required.';
            isValid = false;
        }
        

        // let validInputs = [...this.template.querySelectorAll('lightning-input[data-name="managerName"]') , ...this.template.querySelectorAll('lightning-input[data-name="managerListName"]')].reduce(
        //     (validSoFar, input) => {
        //         console.log('input. value ' + input.value);
                
        //         if(input.checkValidity()) {
        //             input.classList.add("slds-has-error");
        //         } else {
        //             input.classList.remove("slds-has-error");
        //         }

        //         input.reportValidity();
        //         return validSoFar && input.checkValidity();
        //     },
        //     true
        // )

        return isValid;
    }

    isValidOption(selectedManager) {
        return this.managersList.some(option => option.value === selectedManager);
    }

    navigateURL() {
        window.open(this.urlName, "_blank");
    }

    closePage(recordID) {

        let value = {
            'recordID': recordID
        };

        let navigateChange = new CustomEvent('closepage', {
            detail: { value },
            bubbles: true, //set to true to be able to access in grand parent
            composed: true, //set to true to be able to access in grand parent
        });

        this.dispatchEvent(navigateChange);

    }

    showError(message) {

        this.message = message;
        this.displayError = true;
        this.isLoading = false;

        // ldsUtils.scrollTo(this, 'orderItems');

    }

    completeAllSteps() {
        this.processSteps.forEach((step) => {
            step.isCompleted = true;
            step.isProcessing = false;
        });
    }


    updateStepCompletionByIndex(index, status, message, orderId, skip) {
        // Find the step with the matching index
        const step = this.processSteps.find((s) => s.index === index);
        const nextStep = this.processSteps.find((s) => s.index === index + 1);
    
        // If the index is 99, mark all steps as completed
        if (index === 99) {
            this.processSteps.forEach((s) => {
                // s.isCompleted = true;
                s.isProcessing = false;
            });
        } else if (step) {
            // Mark the current step as completed
            if(status == 'failed') {
                if(skip == true || message == 'Could not queue the request for procesing.' || message == 'Customer bank details are missing from the case.') {
                    step.isSkipped = true;
                } else {
                    step.isFailed = true;
                }
                step.isProcessing = false;
                step.errorMessage = message;
                step.showErrorMessage = message.trim().length !== 0;

                if(this.recordIds != undefined && this.recordIds != []) {
                    this.processSteps.forEach((stp) => {
                        if(this.recordIds.includes(stp.cpaId) && stp.cpaId != step.cpaId) {
                            stp.isProcessing = false;
                            stp.errorMessage = message;
                            stp.showErrorMessage = message.trim().length !== 0;
                        }
                    });
                }
            } else {
                step.isFailed = false;
                step.isProcessing = false;
                // step.isSkipped = false;
                step.isCompleted = true;
                if(orderId != '' && orderId != undefined) {
                    step.recordnumber = orderId;
                    step.showCopyToClipboard = true;
                    step.label = step.label + ' - (Ref #'+orderId+')';

                    if(step.label.includes('Warranty Order')) {
                        step.showLinkToTools = true;
                        step.linkToTools = 'https://reporting.emgcloud.net/service-portal/?by=order&search='+orderId;
                    }
                    if(this.recordIds != undefined && this.recordIds != []) {
                        this.processSteps.forEach((stp) => {
                            if(this.recordIds.includes(stp.cpaId) && stp.cpaId != step.cpaId) {
                                stp.isFailed = false;
                                stp.isProcessing = false;
                                // step.isSkipped = false;
                                stp.isCompleted = true;
                                stp.recordnumber = orderId;
                                stp.showCopyToClipboard = true;
                                stp.label = stp.label + ' - (Ref #'+orderId+')';
            
                                if(stp.label.includes('Warranty Order')) {
                                    stp.showLinkToTools = true;
                                    stp.linkToTools = 'https://reporting.emgcloud.net/service-portal/?by=order&search='+orderId;
                                }
                            }
                        });
                        //this.processSteps = JSON.parse(JSON.stringify(this.processSteps));
                    }
                } else {
                    if(this.recordIds != undefined && this.recordIds != []) {
                        this.processSteps.forEach((stp) => {
                            if(this.recordIds.includes(stp.cpaId) && stp.cpaId != step.cpaId) {
                                stp.isFailed = false;
                                sstptep.isProcessing = false;
                                // stp.isSkipped = false;
                                stp.isCompleted = true;
                            }
                        });
                        
                        //this.processSteps = JSON.parse(JSON.stringify(this.processSteps));
                    }
                }
                
            }
            
            // Mark the next step as in processing if it exists
            if (nextStep) {
                nextStep.isProcessing = true;
            }
        }
        
        // debugger;
        let nextIndex = this.findNextItemNumer(index+1);

        this.currentProcessingStep = undefined;
        this.currentProcessingStep = nextIndex;

        if (this.currentProcessingStep === 99) {
            this.processSteps.forEach((s) => {
                // s.isCompleted = true;
                s.isProcessing = false;
            });
            this.showCloseClaimButton = true;
            this.showFooterButtonsOnProcessingModal = true;

            this.processSteps.forEach(element => {
                if(element.isFailed == true || element.isSkipped == true) {
                    this.showCloseClaimButton = false;
                }
            });
        } else {
            let nextStepForProcessing = this.processSteps.find((s) => s.index === nextIndex);
            nextStepForProcessing.isProcessing = true;
        }
    }

    findNextItemNumer(newIndex) {
        // check if next item is skipped
        let newCurrentStep = this.processSteps.find((s) => s.index === newIndex);
        if(newCurrentStep != undefined) {
            if(newCurrentStep.isSkipped == true || newCurrentStep.isCompleted == true) {
                newIndex = newIndex+1;
                let newCurrentStep2 = this.processSteps.find((s) => s.index === newIndex);
                if(newCurrentStep2 != undefined) {
                    if(newCurrentStep2.isSkipped == true || newCurrentStep2.isCompleted == true) { 
                        newIndex = newIndex+1;
                        return this.findNextItemNumer(newIndex);
                    }
                } else {
                    return 99;
                }
            }
        } else {
            return 99;
        }
        return newIndex;
    }

    // Method to update step completion
    updateStepCompletion(stepLabel, nextStepLabel) {
        const step = this.processSteps.find((s) => s.label === stepLabel);
        if (step) {
            step.isCompleted = true;
        }
        let nextStep = this.processSteps.find((s) => s.label === nextStepLabel);
        if (nextStep) {
            nextStep.isProcessing = true;
        }
    }

    resetProcessingSteps() {
        this.processSteps = [
            { label: "Creating Claim", isCompleted: false, isProcessing: true, isFailed:false, index: 0, showErrorMessage: false, isSkipped: false, errorMessage: '', showCopyToClipboard: false, showLinkToTools: false, showPrintReturn: false},
            { label: "Creating Claim Product(s), Claim Product Action(s)", isCompleted: false, isProcessing: false, isFailed:false, isSkipped: false, index: 1, showErrorMessage: false, errorMessage: '', showCopyToClipboard: false, showLinkToTools: false, showPrintReturn: false},
        ];
    }

    async handleCopy(event) {
        let msg = event.target.dataset.recordnumber;
    
        if (navigator.clipboard && window.isSecureContext) {
            this.showToast('Success','Copied to clipboard!', 'success');
          return navigator.clipboard.writeText(msg);
        } else {
          let textArea = document.createElement("textarea");
          textArea.value = msg;
          textArea.style.position = "fixed";
          textArea.style.left = "-999999px";
          textArea.style.top = "-999999px";
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          this.showToast('Success','Copied to clipboard!', 'success');
          return new Promise((res, rej) => {
            document.execCommand("copy") ? res() : rej();
            textArea.remove();
          });
        }
        // this.showToast('Success','Copied to clipboard!', 'success');
    }

    async fetchPdfContent(event) {

        console.log('fetchPdfContent called for recordId: ' + this.assessmentIds);

        try {
            // for(let tempAssessment of this.assessmentIds){
            //     const result = await generatePdfContentAsBase64({ recordId: tempAssessment,isCustomer: true });
            //     console.log('PDF data received from Apex. Filename: ' + result.fileName);

            //     if (result && result.base64Data && result.fileName) {
            //         this.downloadPdfFromBase64(result.base64Data, result.fileName);
            //     } else {
            //         this.showToast('Error', 'Failed to retrieve PDF data from server.', 'error');
            //     }
            // }
            const promises = this.assessmentIds.map(tempAssessment =>
                generatePdfContentAsBase64({ recordId: tempAssessment, isCustomer: true })
            );

            const results = await Promise.all(promises);

            for (const result of results) {
                if (result && result.base64Data && result.fileName) {
                    this.downloadPdfFromBase64(result.base64Data, result.fileName);
                } else {
                    this.showToast('Error', 'Failed to retrieve PDF data for one or more files.', 'error');
                }
            }
        } catch (error) {
            console.error('Error in print assessment form LWC:', error);
            let errorMessage = 'An unknown error occurred while generating PDF.';
            if (error.body && error.body.message) {
                errorMessage = error.body.message;
            } else if (error.message) {
                errorMessage = error.message;
            }
            this.showToast('Error Generating PDF', errorMessage, 'error');
        }
    }
    
    downloadPdfFromBase64(base64Data, fileName) {
        try {
            const sliceSize = 512;
            const byteCharacters = atob(base64Data);
            const byteArrays = [];

            for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
                const slice = byteCharacters.slice(offset, offset + sliceSize);
                const byteNumbers = new Array(slice.length);
                for (let i = 0; i < slice.length; i++) {
                    byteNumbers[i] = slice.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                byteArrays.push(byteArray);
            }

            const blob = new Blob(byteArrays, { type: 'application/pdf' });
            const url = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            console.log('PDF download triggered for: ' + fileName);
            this.showToast('Success', `${fileName} downloaded successfully.`, 'success');

        } catch (e) {
            console.error('Error during client-side PDF download:', e);
            this.showToast('Download Error', 'Could not initiate PDF download.', 'error');
        }
    }

    showToast(title, message, type) {
        const event = new ShowToastEvent({
            title: title,
            message: message,
            variant : type
        });
        this.dispatchEvent(event);
    }

    /****************************************************** */

    get orderID() {
        return this.erpData.order_number ? this.erpData.order_number : '-';
    }

    get email() {

        let email = '-';
        let data = this.erpData;

        if (data.hasOwnProperty('customer')) {
            email = data.customer.email;
        }

        return email;

    }

    get showNextButton() {
        return this.cardData.length > 0 && this.currentItemIndex < this.cardData.length-1 ? true : false;
    }

    get showNextPageButton() {
        return this.cardData.length > 0 && this.currentItemIndex == this.cardData.length-1 ? true : false;
    }

    get nextButtonLabel() {
        return this.currentItemIndex == this.cardData.length-1 ? 'Next Page' : 'Next Item';
    }

    get isCamper() {
        return this.erpData.store_name == '4WD SUPACENTRE (CAMPER TRAILERS)' ? true : false;
    }

    get currentDateTime() {
        return new Date().toISOString();
    }

    get isFirstClaimProduct() {
        return this.currentItemIndex == 0;
    }

    get currentClaimNumber() {
        return this.currentItemIndex + 1;
    }

    get totalClaimsCount() {
        return this.cardData.length;
    }

    get isManagerApprovalSubmissionDisabled() {
        return !(this.managerCode != undefined && this.managerCode != '' && this.selectedManager != undefined && this.selectedManager != '');
    }

    get showManagerName() {
        return (this.selectedManager != undefined && this.selectedManager != '') && this.storeManagersList.length > 0;
    }

    get showManagerCode() {
        return (this.selectedManager != undefined && this.selectedManager != '') &&
        (
            (this.storeManagersList.length > 0 && this.selectedManagerFromList != undefined && this.selectedManagerFromList != '')
            ||
            (this.storeManagersList == undefined || this.storeManagersList == '' && this.storeManagersList.length == 0)
        )
    }

    get disableManagerSelectionForSales() {
        let profileListToDisableManagerSelection = ['Store User', 'Store Manager', 'Warehouse', 'Claim User', 'Claim Manager'];
        return profileListToDisableManagerSelection.includes(this.userProfileName) ? true : false;
    }

    get notACallCenterUser() {
        let callCenterProfiles = ['Claim User', 'Claim Manager', 'Claims Agent'];
        return callCenterProfiles.includes(this.userProfileName) ? false : true;
    }

    get isIssueCategoryIsFreightRefund() {
        return this.data.selectedIssueCategory == 'Transit/Delivery Issue' && this.data.selectedIssueSubCategory == 'Shipping';
    }


    get trueVal() {
        return true;
    }

    // get showIdentifictionLabel(){
    //     for(let temp of this.processSteps){
    //         if(temp.showIdentifictionLabel){
    //             return true;
    //         }
    //     }
    //     return false;
    // }
    /*scrollToElement(event) {
        // Get the ID of the element clicked
        const elementId = event.target.dataset.id;

        // Find the element with the matching ID
        const element = this.template.querySelector(`[data-id="${elementId}"]`);

        if (element) {
            // Perform smooth scroll to the selected element
            window.scrollTo({
                top: element.offsetTop,   // Scroll to the top position of the element
                left: 0,                  // Keep horizontal scroll to 0 (can be changed if needed)
                behavior: 'smooth'        // Smooth scrolling effect
            });
        }
    }*/
}