/**
 * @description Controller for Assessment Form operations
 * <AUTHOR>
 * @group       Assessment Form
 * @last modified on  : 07-14-2025
 */
public with sharing class AssessmentFormController {

     @AuraEnabled(cacheable=true)
    public static Knowledge__kav getKnowledgeArticleById(String articleVersionName) {
        if (String.isBlank(articleVersionName)) {
            return null;
        }
        articleVersionName = '%' + articleVersionName + '%';
        return [
            SELECT Id, Title, Summary, ArticleNumber, UrlName, LastModifiedDate, LastPublishedDate,Details__c 
            FROM Knowledge__kav
            WHERE Title Like :articleVersionName AND PublishStatus = 'Online' AND Language = 'en_US'
            LIMIT 1
        ];
    }

        @AuraEnabled
    public static Map<String,String> getClaimProductById(Id recordId) {
        try {
                Id claimProductId;
                Schema.SObjectType sObjType = recordId.getSObjectType();
                Map<String,String> returnMap = new Map<String,String>();

                // Get the API name of the sObject
                String apiName = sObjType.getDescribe().getName();

                if(apiName == 'Assessment_Form__c'){
                    Assessment_Form__c af = [SELECT Id,Can_the_test_be_started_now_or_later__c, Customer_Stated_Issue__c,Claim_Product__c, Can_the_product_be_charged_now__c, AC_DC_Charge_Start_Time_Date__c, AC_DC_Charge_Finish_Time_Date__c, Voltage_after_charging__c, Did_the_product_charge_correctly__c,Jump_Started_Required__c,Voltage_on_Arrival__c,Physical_Condition__c,Load_Test_Start_Time_Date__c,Load_Test_Finish_Time_Date__c,Tested_Capacity_AH__c,Assessment_Outcome__c FROM Assessment_Form__c WHERE Id = :recordId LIMIT 1];
                    returnMap.put('assessmentForm',JSON.serialize(af));
                    claimProductId = af.Claim_Product__c;
                }else{
                    returnMap.put('assessmentForm','{}');
                    claimProductId = recordId;
                }

                Claim_Product__c cp = [
                    SELECT Id, Name, SKU__c, Product_Id__c,Product_Id__r.Battrey_Capacity__r.X90_of_Capacity__c,Product_Id__r.Battrey_Capacity__r.Name,Product_Id__r.Battrey_Capacity__r.Required_Discharge_Amperage__c, Item_Price__c,
                        Batch_Number__c, Serial_Number__c, Reason__c, Reason_Sub_Category__c,
                        Status__c, Case_Id__c, Shipping_Status__c, Assessment_Status__c,
                        Assessment_Outcome__c, CreatedDate, Product_Name__c, Item_Price_Currency__c,
                        Supplier_Account__c,Did_this_Faulty_Product_cause_Damage__c,Assessment_Fault_Category_value__c,Assesment_Fault_Description__c 
                    FROM Claim_Product__c
                    WHERE Id = :claimProductId
                    LIMIT 1
                ];
                Map<String,String> cpObj = new Map<String,String>();
                cpObj.put('willSparePartCanFixIssue',cp.Assessment_Outcome__c == 'Replacement Part' ? 'Yes' : 'No');
                cpObj.put('prodName',cp.Product_Name__c);
                cpObj.put('sku',cp.SKU__c);
                cpObj.put('assessmentFaultCategoryLable',cp.Assessment_Fault_Category_value__c);
                cpObj.put('assessmentFaultDescription',cp.Assesment_Fault_Description__c);
                cpObj.put('selectedIssueCategory',cp.Reason__c);
                cpObj.put('selectedIssueSubCategory',cp.Reason_Sub_Category__c);
                cpObj.put('batchValue',cp.Batch_Number__c);
                cpObj.put('shippingMethod',cp.Shipping_Status__c);
                cpObj.put('didCauseDamage',cp.Did_this_Faulty_Product_cause_Damage__c);
                if(cp.Product_Id__r.Battrey_Capacity__r != null){
                    cpObj.put('battreyTableInfo',JSON.serialize(cp.Product_Id__r.Battrey_Capacity__r));
                }

                returnMap.put('cpObj',JSON.serialize(cpObj));

                List<ContentDocumentLink> cdl = [SELECT ContentDocumentId,ContentDocument.LatestPublishedVersionId FROM ContentDocumentLink WHERE LinkedEntityId = :claimProductId AND ContentDocument.LatestPublishedVersion.File_category_fileupload__c = 'Batch Location Photo' LIMIT 1];
                if(!cdl.isEmpty()) {
                    returnMap.put('batchFileIds',cdl[0].ContentDocument.latestPublishedVersionId);
                } else {
                    returnMap.put('batchFileIds','');
                }
                return returnMap;
        } catch (Exception e) {
            throw new AuraHandledException('Error fetching Claim Product: ' + e.getMessage());
        }
    }
    
    /**
     * @description Create Assessment Form record with validation
     * @param claimProductId The Claim Product ID to associate with the Assessment Form
     * @param assessmentData The Assessment Form data from the component
     * @return String Assessment Form ID if successful
     */
    @AuraEnabled
    public static String createAssessmentForm(Id claimProductId, String assessmentData) {
        try {
            // Validate required parameters
            if (claimProductId == null) {
                throw new AuraHandledException('Claim Product ID is required.');
            }
            
            if (assessmentData == null) {
                throw new AuraHandledException('Assessment data is required.');
            }
            
            // Validate required fields
            // validateRequiredFields(assessmentData);
            
            // Create Assessment Form record
            Assessment_Form__c assessmentForm = (Assessment_Form__c)JSON.deserialize(assessmentData, Assessment_Form__c.class);
            if(assessmentForm.Id == null){
                assessmentForm.Claim_Product__c = claimProductId;
            }

            assessmentForm.RecordTypeId = Schema.getGlobalDescribe().get('Assessment_Form__c'.toLowerCase()).getDescribe().getRecordTypeInfosByDeveloperName().get('Battery_Assessment_Form')?.getRecordTypeId();
            
            // Map the assessment data to Assessment Form fields
            // mapAssessmentData(assessmentForm, assessmentData);
            
            // upsert the record
            upsert assessmentForm;
            
            return assessmentForm.Id;
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Error creating Assessment Form: ' + e.getMessage());
            throw new AuraHandledException('Error creating Assessment Form: ' + e.getMessage());
        }
    }
    
    /**
     * @description Update existing Assessment Form record
     * @param assessmentFormId The Assessment Form ID to update
     * @param assessmentData The updated Assessment Form data
     * @return String Assessment Form ID if successful
     */
    // @AuraEnabled
    // public static String updateAssessmentForm(Id assessmentFormId, Map<String, Object> assessmentData) {
    //     try {
    //         // Validate required parameters
    //         if (assessmentFormId == null) {
    //             throw new AuraHandledException('Assessment Form ID is required.');
    //         }
            
    //         if (assessmentData == null || assessmentData.isEmpty()) {
    //             throw new AuraHandledException('Assessment data is required.');
    //         }
            
    //         // Validate required fields
    //         validateRequiredFields(assessmentData);
            
    //         // Get existing Assessment Form record
    //         Assessment_Form__c assessmentForm = [
    //             SELECT Id, Claim_Product__c 
    //             FROM Assessment_Form__c 
    //             WHERE Id = :assessmentFormId 
    //             LIMIT 1
    //         ];
            
    //         // Map the assessment data to Assessment Form fields
    //         mapAssessmentData(assessmentForm, assessmentData);
            
    //         // Update the record
    //         update assessmentForm;
            
    //         return assessmentForm.Id;
            
    //     } catch (Exception e) {
    //         System.debug(LoggingLevel.ERROR, 'Error updating Assessment Form: ' + e.getMessage());
    //         throw new AuraHandledException('Error updating Assessment Form: ' + e.getMessage());
    //     }
    // }
    
    // /**
    //  * @description Get existing Assessment Form for a Claim Product
    //  * @param claimProductId The Claim Product ID
    //  * @return Assessment_Form__c The existing Assessment Form or null
    //  */
    // @AuraEnabled(cacheable=true)
    // public static Assessment_Form__c getAssessmentFormByClaimProduct(Id claimProductId) {
    //     try {
    //         if (claimProductId == null) {
    //             return null;
    //         }
            
    //         List<Assessment_Form__c> assessmentForms = [
    //             SELECT Id, Physical_Condition__c, Fridge_Issue__c, Other__c, 
    //                    Fridge_Issue_Occurs_On__c, Model__c, DC_Lead__c, 
    //                    Assessment_Outcome__c, Basket__c, Handles__c, 
    //                    AC_Cable__c, Anderson_Cable__c, Notes__c,
    //                    Pictures_On_File_Of_Overall_Cosmetic__c,
    //                    Fault_Found_Repaired_Recommendations_Ref__c,
    //                    Load_Test_Start_Time_Date__c, Load_Test_Finish_Time_Date__c,
    //                    Tested_Capacity_AH__c
    //             FROM Assessment_Form__c 
    //             WHERE Claim_Product__c = :claimProductId 
    //             ORDER BY CreatedDate DESC 
    //             LIMIT 1
    //         ];
            
    //         return assessmentForms.isEmpty() ? null : assessmentForms[0];
            
    //     } catch (Exception e) {
    //         System.debug(LoggingLevel.ERROR, 'Error getting Assessment Form: ' + e.getMessage());
    //         throw new AuraHandledException('Error getting Assessment Form: ' + e.getMessage());
    //     }
    // }
    
    /**
     * @description Validate required fields for Assessment Form
     * @param assessmentData The assessment data to validate
     */
    private static void validateRequiredFields(Map<String, Object> assessmentData) {
        List<String> missingFields = new List<String>();
        
        // Check Pictures On File (mandatory)
        if (assessmentData.get('Pictures_On_File_Of_Overall_Cosmetic__c') != true) {
            missingFields.add('Pictures On File Of Overall Cosmetic');
        }
        
        // Check Load Test Start Time/Date (mandatory for battery testing)
        if (String.isBlank((String)assessmentData.get('Load_Test_Start_Time_Date__c'))) {
            missingFields.add('Load Test Start Time/Date');
        }
        
        // Check Load Test Finish Time/Date (mandatory for battery testing)
        if (String.isBlank((String)assessmentData.get('Load_Test_Finish_Time_Date__c'))) {
            missingFields.add('Load Test Finish Time/Date');
        }
        
        // Check Tested Capacity (mandatory for battery testing)
        if (String.isBlank((String)assessmentData.get('Tested_Capacity_AH__c'))) {
            missingFields.add('Tested Capacity (AH)');
        }
        
        // Check Assessment Outcome (mandatory)
        if (String.isBlank((String)assessmentData.get('Assessment_Outcome__c'))) {
            missingFields.add('Assessment Outcome');
        }
        
        if (!missingFields.isEmpty()) {
            throw new AuraHandledException('Please complete the following required fields: ' + String.join(missingFields, ', '));
        }
    }
    
    /**
     * @description Map assessment data to Assessment Form fields
     * @param assessmentForm The Assessment Form record to populate
     * @param assessmentData The data from the component
     */
    private static void mapAssessmentData(Assessment_Form__c assessmentForm, Map<String, Object> assessmentData) {
        // Map all the fields from the component
        if (assessmentData.containsKey('Physical_Condition__c')) {
            assessmentForm.Physical_Condition__c = (String)assessmentData.get('Physical_Condition__c');
        }
        if (assessmentData.containsKey('Fridge_Issue__c')) {
            assessmentForm.Fridge_Issue__c = (String)assessmentData.get('Fridge_Issue__c');
        }
        if (assessmentData.containsKey('Other__c')) {
            assessmentForm.Other__c = (String)assessmentData.get('Other__c');
        }
        if (assessmentData.containsKey('Fridge_Issue_Occurs_On__c')) {
            assessmentForm.Fridge_Issue_Occurs_On__c = (String)assessmentData.get('Fridge_Issue_Occurs_On__c');
        }
        if (assessmentData.containsKey('Model__c')) {
            assessmentForm.Model__c = (String)assessmentData.get('Model__c');
        }
        if (assessmentData.containsKey('DC_Lead__c')) {
            assessmentForm.DC_Lead__c = (String)assessmentData.get('DC_Lead__c');
        }
        if (assessmentData.containsKey('Assessment_Outcome__c')) {
            assessmentForm.Assessment_Outcome__c = (String)assessmentData.get('Assessment_Outcome__c');
        }
        if (assessmentData.containsKey('Basket__c')) {
            assessmentForm.Basket__c = (Boolean)assessmentData.get('Basket__c');
        }
        if (assessmentData.containsKey('Handles__c')) {
            assessmentForm.Handles__c = (Boolean)assessmentData.get('Handles__c');
        }
        if (assessmentData.containsKey('AC_Cable__c')) {
            assessmentForm.AC_Cable__c = (Boolean)assessmentData.get('AC_Cable__c');
        }
        if (assessmentData.containsKey('Anderson_Cable__c')) {
            assessmentForm.Anderson_Cable__c = (Boolean)assessmentData.get('Anderson_Cable__c');
        }
        if (assessmentData.containsKey('Notes__c')) {
            assessmentForm.Notes__c = (String)assessmentData.get('Notes__c');
        }
        if (assessmentData.containsKey('Pictures_On_File_Of_Overall_Cosmetic__c')) {
            assessmentForm.Pictures_On_File_Of_Overall_Cosmetic__c = (Boolean)assessmentData.get('Pictures_On_File_Of_Overall_Cosmetic__c');
        }
        if (assessmentData.containsKey('Fault_Found_Repaired_Recommendations_Ref__c')) {
            assessmentForm.Fault_Found_Repaired_Recommendations_Ref__c = (String)assessmentData.get('Fault_Found_Repaired_Recommendations_Ref__c');
        }
        
        // Map Step 3 fields (battery testing)
        if (assessmentData.containsKey('Load_Test_Start_Time_Date__c')) {
            String dateTimeStr = (String)assessmentData.get('Load_Test_Start_Time_Date__c');
            if (String.isNotBlank(dateTimeStr)) {
                assessmentForm.Load_Test_Start_Time_Date__c = DateTime.valueOf(dateTimeStr);
            }
        }
        if (assessmentData.containsKey('Load_Test_Finish_Time_Date__c')) {
            String dateTimeStr = (String)assessmentData.get('Load_Test_Finish_Time_Date__c');
            if (String.isNotBlank(dateTimeStr)) {
                assessmentForm.Load_Test_Finish_Time_Date__c = DateTime.valueOf(dateTimeStr);
            }
        }
        if (assessmentData.containsKey('Tested_Capacity_AH__c')) {
            assessmentForm.Tested_Capacity_AH__c = (String)assessmentData.get('Tested_Capacity_AH__c');
        }
    }
}
