import { LightningElement, api, track, wire} from 'lwc';

import deleteUploadedFiles from '@salesforce/apex/LightningUtilities.deleteUploadedFiles';                                  // import deleteUploadedFiles 
import isValidBatchNumberApex from '@salesforce/apex/LightningUtilities.isValidBatchNumber';  
import getContentDocumentIdsBySKU from '@salesforce/apex/LightningUtilities.getContentDocumentIdsBySKU';
import getShippingLocationList from '@salesforce/apex/LightningUtilities.getShippingLocationList';          // import getShippingLocationList method from 
import getActiveCouriers from '@salesforce/apex/LightningUtilities.getActiveCouriers';                                  // import getActiveCouriers from 
import getShipToAddress from '@salesforce/apex/LightningUtilities.getShipToAddress';                                    // import getShipToAddress from 
import getRecordTypes from '@salesforce/apex/LightningUtilities.getRecordTypes';
import getReturnPalletInfo from '@salesforce/apex/LightningUtilities.getReturnPalletInfo';
import getShippingLocations from '@salesforce/apex/LightningUtilities.getShippingLocations';                                    // import getShipToAddress from LightningUtilities Apex Class


import getCategoriesBySKU from '@salesforce/apex/ProductAssessmentFaultCategoryController.getCategoriesBySKU';
import getOtherAssessmentId from '@salesforce/apex/ProductAssessmentFaultCategoryController.getOtherAssessmentId';
import isCreatedByGuestUser from '@salesforce/apex/LightningUtilities.isCreatedByGuestUser';
import isUserLocationAllowedForAssessmentForm from '@salesforce/apex/AssessmentFormController.isUserLocationAllowedForAssessmentForm';

import SELECTED_RECORD_TYPE_FIELD from '@salesforce/schema/Assessment_Form__c.Selected_Record_Type__c';          // import Claim Product Action - Approved By: Staff Member Field Schema



//USER OBJECT FIELDS
import USER_4WD_LOCATION_FIELD from '@salesforce/schema/User.X4WD_Locations__c';                             // import User - 4WD Location Field Schema
import USER_PROFILE_NAME from '@salesforce/schema/User.Profile.Name';                                       // import User - Profile Name
import USER_NAME from '@salesforce/schema/User.Name';                                                       // import User - Name
import USER_ID from '@salesforce/user/Id';     

import { getRecord, getFieldValue } from 'lightning/uiRecordApi';                                           // import getRecord, getFieldValue method from uiRecordApi
import * as ldsUtils from 'c/ldsUtils'; 
import {NavigationMixin} from 'lightning/navigation';  
import {ShowToastEvent} from 'lightning/platformShowToastEvent';

const STANDARD_DATE = new Date();                                                                                       // value of current date
STANDARD_DATE.setDate(STANDARD_DATE.getDate() + 4);

const STRING_MIN_DATE = String(STANDARD_DATE.getFullYear()).padStart(2, '0') + '-' +                                    // value of current date plus 4 days
                        String((STANDARD_DATE.getMonth() + 1)).padStart(2, '0') + '-' +
                        String(STANDARD_DATE.getDate()).padStart(2, '0');

export default class ClaimFormCpItemProductIssueChild extends NavigationMixin(LightningElement) {
    //Resume Claim process properties
    @api sfClaimProductActionID;
    @api sfClaimProductID;
    @api sfCaseID;
    @track locationMap = [];
    @track locationMapRes = [];

    @track requireFileUploadCheck = true;
    @track data;
    @track uploadedFiles = [];
    doneTypingInterval = 500;
    typingTimer;
    batchTypingTimer;
    @track isAssessmentFaultDescriptionDisabled = true;
    @track showOpenBatchPhotoLocationModel = false;
    @track showOpenSerialPhotoLocationModel = false;
    @track batchPhotoLocationContentDocumentId;
    @track serialPhotoLocationContentDocumentId;
    @track showPreviewBatchPhoto = false;
    @track showPreviewSerialPhoto = false;

    userID = USER_ID;                   // Current User ID.
    userLocation;                       // Current User Location 
    userProfileName;                    // Current User Profile Name
    defaultAddressAdded=false;
    shippingLocationUpdatedByCustomLogic= false;
    @track shippingLocationOptions = [];       // Shipping Location Options.
    shippingLocationSelected = '';      // Shipping Location Value.
    standardShippingLocationId = '';    // Standard Shipping Location ID.
    @track shipToAddress = {};
    @track courierOptions = [];
    @track courierMap = {};
    @track shippingToOptions = [];
    @track shippingToList = [];
    @track isRefrigerationType = false;
    @track isbatteryType = false;

    
    pickupDateMin = STRING_MIN_DATE;    // Minimum Pickup Date Value.
    pickupDate = STRING_MIN_DATE;       // Selected Pickup Date Value.
    pickupTimeStart = '09:00:00.000';   // Selected Pickup Start Time Value.
    pickupTimeEnd = '17:00:00.000';     // Selected Pickup End Time Value.

    //Assessment fields
    @track showAddAssessmentFormModal = false;
    @track assessmentFormColumns = [
        { label: 'Order Date', fieldName: 'Purchase_Date__c', type: 'date' },
        { label: 'Is Product in Warrenty', fieldName: 'Product_is_in_warranty__c', type: 'checkbox' },
        { label: 'Physical Condition', fieldName: 'Physical_Condition__c', type: 'text' },
        { label: 'Notes', fieldName: 'Notes__c', type: 'text' },
        { type: 'button', typeAttributes: { label: 'Edit', name: 'edit', title: 'edit', disabled: false, value: 'edit', iconPosition: 'left' }},
        { type: 'button', typeAttributes: { label: 'Delete', name: 'delete', title: 'delete', disabled: false, value: 'delete', iconPosition: 'left' }},
        { type: 'button', typeAttributes: { label: 'Print', name: 'print', title: 'print', disabled: false, value: 'print', iconPosition: 'left' }}
        // Add other columns as required
      ];
    @track assessmentTableData = [];
    @track assessmentRecordTypes = [];
    @track currentAssessmentForm = {};
    @track assessmentFaultCategories = [];
    isAddBackToStockLocationChangeDisabled = false;
    addBackToStockActionDisabled = false;
    userDefaultAddBackToStockLocation = '';
    @track disabledOutcome = false;
    @track isNewAssessmentFormVisible = false;

    /// Return Pallte
    @track filterForReturnPallet = {
        criteria: [
            {
                fieldPath: 'Status__c',
                operator: 'eq',
                value: 'New',
            }
        ],
        filterLogic: '1',
    };
    @track showAddReturnPalletModal = false;
    @track isGuest = false;

    @api erpData;
    @api serialBatchUrl;
    @api showEditQtyButton = false;

    @api index=0;
    @api selectedIssueCategory;
    @api approvalScreen = false;
    @api currentProductCategory;
    @api
    get cpObj() {
        return this.data;
    }
    set cpObj(value) {
        this.data = JSON.parse(JSON.stringify(value));
        this.currentAssessmentForm =  this.data.assessmentTableData ? (this.data.assessmentTableData.length>0 ? this.data.assessmentTableData[0] : {}) : {};
        console.log('assessmentForm --',this.currentAssessmentForm);
        if(this.data.batchValue != '' && this.data.batchValue != undefined) {
            this.validateBatchNumber(this.data.batchValue, this.data);
        }
    }

    @api
    get fullCardData() {
        return this.cardData;
    }
    set fullCardData(value) {
        this.cardData = JSON.parse(JSON.stringify(value));
    }


    get additionalFiltersForRP() {
        if (this.userLocation) {
            const locations = this.userLocation.split(';');
            const formattedLocations = locations.map(location => `'${location.trim()}'`).join(',');
            return `Status__c = 'New' AND From_Location__c IN (${formattedLocations})`;
        }
        return `Status__c = 'New'`;
    }


    get isFilesUploaded() {
        if(this.data.uploadedFiles != undefined && this.data.uploadedFiles != [] &&  this.data.uploadedFiles.length > 0) {
            return true;
        }
        return false;
    }

    get trueVal() {
        return true;
    }

    get isCPReasonMissingPart() {
        return this.data.selectedIssueSubCategory != undefined && this.data.selectedIssueSubCategory != '' && this.data.selectedIssueSubCategory == 'Part Missing';
    }


    get canShowAssessmentScreen() {
        // Check if all required data is present and not empty
        return this.data &&
               this.data.uploadedFiles != undefined & this.data.uploadedFiles != [] && this.data.uploadedFiles.length >= 0 
               //this.validateInput();  // Ensure forms are checked
    }

    get isAssessmentCompleted() {
        // Check if all required data is present and not empty
        return this.data &&
               (
                (   
                    this.data.assessmentOutcome != undefined && this.data.assessmentOutcome != '' && 
                    this.data.assessmentStaffName != undefined && this.data.assessmentStaffName != ''
                )
               )
    }

    get isAssessmentCompletedAndOutcomeIsReplacement() {
        // Check if all required data is present and not empty
        console.log('in ');
        console.log('this.data -> ' + JSON.stringify(this.data, null, 2));
        console.log('assessment -> ', (
            (
                this.data.assessmentOutcome != undefined & this.data.assessmentOutcome != '' && 
                this.data.assessmentStaffName != undefined & this.data.assessmentStaffName != ''
            )
           ));
        console.log('resolution ->', (this.data.resolutionOutcome != undefined & this.data.resolutionOutcome != '' && (this.data.resolutionOutcome == 'Replacement Part' || this.data.resolutionOutcome == 'Replacement Item')));
        console.log('cause -> ', (this.data.didCauseDamage == undefined || this.data.didCauseDamage == 'No'));

        return this.data &&
               (
                (
                    this.data.assessmentOutcome != undefined & this.data.assessmentOutcome != '' && 
                    this.data.assessmentStaffName != undefined & this.data.assessmentStaffName != ''
                )
               ) &&
               (this.data.resolutionOutcome != undefined & this.data.resolutionOutcome != '' && (this.data.resolutionOutcome == 'Replacement Part' || this.data.resolutionOutcome == 'Replacement Item'))
               && 
                ((this.data.didCauseDamage == undefined || this.data.didCauseDamage == 'No') || this.sfClaimProductActionID != undefined)

    }

    get isOutcomeIsMoneyBack() {
        return this.data.resolutionOutcome != undefined & this.data.resolutionOutcome != '' && this.data.resolutionOutcome == 'Money Back';
    }


    get isAssessmentFaultCategoryEntered() {
        return (!(this.data && (this.data.assessmentFaultCategory && this.data.assessmentFaultCategory != undefined && this.data.assessmentFaultCategory != ''))) || this.approvalScreen;
    }

    get isAssessmentFaultDescriptionEntered() {
        return (!(this.data && (this.data.assessmentFaultDescription && this.data.assessmentFaultDescription != undefined && this.data.assessmentFaultDescription != ''))) || this.approvalScreen;z
    }
    
    get isAssessmentOutcomeSelected() {
        return (!(!this.isAssessmentFaultDescriptionEntered && 
                this.data && this.data.assessmentOutcome && this.data.assessmentOutcome != undefined && this.data.assessmentOutcome != '')) || this.approvalScreen;
    }

    get isresolutionOutcomeSelected() {
        return this.data && this.data.resolutionOutcome && this.data.resolutionOutcome != undefined && this.data.resolutionOutcome != '';
    }

    get isDoingAssessmentNow() {
        return this.data && this.data.assesmentPossible && this.data.assesmentPossible != undefined && this.data.assesmentPossible == 'Now';
    }

    get isDoingAssessmentLater() {
        return this.data && this.data.assesmentPossible && this.data.assesmentPossible != undefined && this.data.assesmentPossible == 'Later';
    }

    get showOutcomeDetails() {
        console.log('in showOutcome Detials ');
        console.log('this.isAssessmentCompleted -> ' + this.isAssessmentCompleted);
        console.log('this.data.didCauseDamage -> ' + this.data.didCauseDamage);
        // return this.isAssessmentCompleted || this.isDoingAssessmentLater || this.data.willSparePartCanFixIssue == 'Yes';
        return (this.isAssessmentCompleted && (this.data.didCauseDamage == undefined || this.data.didCauseDamage == 'No'));
    }

    get isReturnMethodIsPrepaidConsignment() {
        return this.data.returnMethod != undefined && this.data.returnMethod == 'Prepaid Consignment';
    }

    get isAssesmentPossibleDisabled() {
        return this.approvalScreen || (this.data.assesmentPossibleDisabled != undefined && this.data.assesmentPossibleDisabled == true);
    }

    get disableCustomerOutcome() {
        console.log('this.data.disableCustomerOutcome -> ' + JSON.stringify(this.data.disableCustomerOutcome, null, 2));
        return this.approvalScreen || (this.data.disableCustomerOutcome != undefined && this.data.disableCustomerOutcome == true);
    }

    get isPartsOnlyFromSparePartFix() {
        return this.data.willSparePartCanFixIssue == 'Yes';
    }

    /*
     * No Parameters.
     * Populate the Street Field for the Shipping To Address Field.
    */
    get streetName() {
        return this.data?.shipToAddress?.street[0];
    }

    get isCourierBorderExpress() {
        return this.isReturnMethodIsPrepaidConsignment && this.data?.courierName == 'Border Express';
    }

    get isAssessmentOutcomeIsKnownFault() {
        return this.data.assessmentOutcome == 'Faulty';
    }
    
    get isClaimProductFinalOutcomeAddBackToStockParts() {
        return this.data.claimProductFinalOutcome == 'Add Back To Stock (Parts)';
    }

    get isClaimProductFinalOutcomeProductDisposed() {
        return this.data.claimProductFinalOutcome == 'Product Disposed' && this.data.warrantyClaimDCDisposalCompulsory != true;
    }

    get claimProductFinalOutcomeOptions() { 
        let options = [
            {
                label:'Add Back To Stock (Seconds)',
                value:'Add Back To Stock (Seconds)'
            },
            {
                label:'Add Back To Stock (Parts)',
                value:'Add Back To Stock (Parts)'
            },
            {
                label:'Product Disposed',
                value: 'Product Disposed'
            }
            // {
            //     label:'Returned to Customer',
            //     value: 'Returned to Customer'
            // }
        ]

        if(this.data && this.data.assessmentOutcome != undefined && this.data.assessmentOutcome != '' && this.data.assessmentOutcome !='Known Fault') {
            options.push(
                {
                    label:'Returned to Customer',
                    value: 'Returned to Customer'
                }
            )
        }

        return options;
    }

    get showCPItemLocation() {
        return this.data && (this.data.claimProductFinalOutcome == 'Add Back To Stock (Seconds)' || this.data.claimProductFinalOutcome == 'Add Back To Stock (Parts)' ) && this.sfClaimProductActionID == undefined;
    }

    get getShowReturnPalletWindow() {
        let failedOutcomes = ['Faulty', 'Goodwill'];

        return (
               (this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') && this.data.assessmentOutcome != undefined && this.data.assessmentOutcome != '' && failedOutcomes.includes(this.data.assessmentOutcome)
        );
    }

    get showCreateDCReturn() {
        let failedOutcomes = ['Faulty', 'Goodwill'];

        return (
            (this.isAssessmentCompleted && this.data.warrantyClaimDCDisposalCompulsory == true && this.data.claimProductFinalOutcome == 'Product Disposed' && this.sfClaimProductActionID == undefined));
        //     ||
        //     ( this.userProfileName == 'Store User' && this.data.assessmentOutcome != undefined && this.data.assessmentOutcome != '' && failedOutcomes.includes(this.data.assessmentOutcome))    
        // );
    }

    get showDisposalWarning() {
        return  (this.isAssessmentCompleted && this.data.warrantyClaimDCDisposalCompulsory == true && this.data.claimProductFinalOutcome == 'Product Disposed' && this.sfClaimProductActionID == undefined);
    }

    get showAssessmentFaultCategoryOther() {
        return this.data.assessmentFaultCategoryLable === 'Other' || this.data.assessmentFaultCategoryLable === '' || this.data.assessmentFaultCategoryLable == null ;
    }
    get diableAssesmentOutcome(){
        return ((this.data.assessmentFaultCategory === 'Other' && ( this.data.assessmentFaultDescription == null || this.data.assessmentFaultDescription === '') || this.data.assessmentFaultCategory === '' || this.data.assessmentFaultCategory === null))
    }
    
    get assessmentFaultDescriptionHelpText() {
        let helpText = '';
        if(this.assessmentFaultCategories.length > 0) {
            this.assessmentFaultCategories.forEach(element => {
                if(this.data.assessmentFaultCategory == element.Assessment_Fault_Category_Name__c) {
                    helpText = element.Help_Text__c;
                }
            });
        }
        return helpText;
    }

    get isProductFaultPhotoVideoRequired() {
        return this.data.didCauseDamage != undefined && this.data.didCauseDamage == 'No';
    }

    get showCreateReturnOption() {
        return this.isGuest === true || (this.data.assesmentPossible == 'Later' && this.sfClaimProductActionID == undefined && (this.userProfileName != 'Store User' && this.userProfileName != 'Store Manager'));
        // return this.isGuest === true || this.sfClaimProductActionID === undefined;
    }

    get productReturnActionCreateReturn() {
        return this.data.productReturnAction != undefined && this.data.productReturnAction == 'Yes';   
    }

    get disableFireRelatedStuff() {
        return this.approvalScreen || this.data.didCauseDamage == 'Yes';
    }

    get showAddBackToStockLocationOptions() {
        return this.data != undefined && this.data.claimProductFinalOutcome != undefined && (this.data.claimProductFinalOutcome =='Add Back To Stock (Seconds)' || this.data.claimProductFinalOutcome =='Add Back To Stock (Parts)');
    }

    get addBackToStockLocationOptions() {
        if (
            (this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') ||
            (this.userProfileName == 'Warehouse' && (this.userLocation != undefined && this.userLocation.endsWith("DC"))) 
        ) {
            let locations = this.userLocation.split(';');
            let returnArr = [];
            locations.forEach(element => {
                returnArr.push({ label: element.trim(), value: element.trim() });
            });  
            return returnArr;
        }
        else {
            console.log('else is calling');
            /*return
             [
                { label: 'Brendale', value: 'Brendale' },
                { label: 'Brisbane DC', value: 'Brisbane DC' },
                { label: 'Bunbury', value: 'Bunbury' },
                { label: 'Cairns', value: 'Cairns' },
                { label: 'Campbellfield', value: 'Campbellfield' },
                { label: 'Campbelltown', value: 'Campbelltown' },
                { label: 'Canning Vale', value: 'Canning Vale' },
                { label: 'Coffs Harbour', value: 'Coffs Harbour' },
                { label: 'Eastern Creek', value: 'Eastern Creek' },
                { label: 'Fyshwick', value: 'Fyshwick' },
                { label: 'Geraldton', value: 'Geraldton' },
                { label: 'Gosford', value: 'Gosford' },
                { label: 'Kilburn', value: 'Kilburn' },
                { label: 'Mackay', value: 'Mackay' },
                { label: 'Malaga', value: 'Malaga' },
                { label: 'Melbourne DC', value: 'Dandenong South DC' },
                { label: 'Newcastle', value: 'Newcastle' },
                { label: 'Parkinson', value: 'Parkinson' },
                { label: 'Perth DC', value: 'Perth DC' },
                { label: 'Ravenhall', value: 'Ravenhall' },
                { label: 'Rockhampton', value: 'Rockhampton' },
                { label: 'Sunshine Coast', value: 'Sunshine Coast' },
                { label: 'Sydney DC', value: 'Sydney DC' },
                { label: 'Toowoomba', value: 'Toowoomba' },
                { label: 'Townsville DC', value: 'Townsville DC' },
                { label: 'Townsville Store', value: 'Townsville' },
                { label: 'Varsity', value: 'Varsity Lakes' },
                { label: 'Wetherill', value: 'Wetherill Park' },
                { label: 'Ballarat', value: 'Ballarat' },
                { label: 'Bendigo', value: 'Bendigo' },
                { label: 'Mildura', value: 'Mildura' },
                { label: 'Shepparton', value: 'Shepparton' },
                { label: 'Dandenong', value: 'Dandenong' },
                {label: 'Wagga Wagga', value: 'Wagga Wagga'},
                {label: 'Wodonga', value: 'Wodonga'}
            ];*/
           return this.locationMap;
        }
    }

   /* @wire(getShippingLocations)
    getShippingLocations({ error, data }) {
        if (data) {
         
            this.locationMap = data.map(location => {
                const cityNameRes = location.Shipping_Method_API_Name__c.trim();
                const cleanedCityName = cityNameRes
                    .replace(/^pickup_/, '') 
                    .replace(/_/g, ' ')
                    .split(' ')               
                    .map((word, index) => {
                        if (index === 0) {
                            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(); 
                        }
                        return word.toLowerCase();
                    })
                    .join(' '); 
                return {
                    label: cleanedCityName, 
                    value: cleanedCityName 
                };
            });
           
            this.locationMapRes = data.reduce((map, location) => {
                const cityNameRes = location.Shipping_Method_API_Name__c.trim(); 
                const cleanedCityNameRes = cityNameRes
                    .replace(/^pickup_/, '') 
                    .replace(/_/g, ' ')
                    .split(' ')              
                    .map((word, index) => {
                        if (index === 0) {
                            return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                        }
                        return word.toLowerCase(); 
                    })
                    .join(' '); 
                
                if (cleanedCityNameRes) {
                    map[cleanedCityNameRes] = cleanedCityNameRes;
                }
                return map;
            }, {});
    
            console.log('All Location Map:', JSON.stringify(this.locationMap));
        } else if (error) {
            console.error('Error fetching shipping locations:', error);
        }
    }*/
        @wire(getShippingLocations)
        getShippingLocations({ error, data }) {
            if (data) {
              
                this.locationMap = data.map(location => ({
                    label: location.Locations__c, 
                    value: location.Locations__c 
                }));
                this.locationMapRes = data.reduce((map, location) => {
                    const cityName = location.Locations__c;
                    
                    if (cityName) {  
                        map[cityName] = cityName;
                    } 
                    return map;
                }, {});
              
                console.log('All Location Map:', JSON.stringify(this.locationMap));
            } else if (error) {
                console.error('Error fetching shipping locations:', error);
            } 
    
        }
    //PICKLIST VALUES
    changeOfMindSubCategoryOptions = [
        {
            label:'Advertised at a lower price',
            value:'Advertised at a lower price'
        },
        {
            label:'Product Not As Described',
            value:'Product Not As Described'
        },
        {
            label:'Does not suit customer needs',
            value:'Does not suit customer needs'
        },
        {
            label:'Other',
            value:'Other'
        },
        {
            label:'Not aware Pre Order Date',
            value:'Not aware Pre Order Date'
        },
    ];

    Options = [
        {
            label:'Advertised at a lower price',
            value:'Advertised at a lower price'
        },
        {
            label:'Product Not As Described',
            value:'Product Not As Described'
        },
        {
            label:'Does not suit customer needs',
            value:'Does not suit customer needs'
        },
        {
            label:'Other',
            value:'Other'
        },
        {
            label:'Not aware Pre Order Date',
            value:'Not aware Pre Order Date'
        },
    ];


    checkboxOptions = [
        // {
        //     label:'Please Select',
        //     value: undefined
        // },
        {
            label:'No',
            value:'No'
        },
        {
            label:'Yes',
            value:'Yes'
        }
    ]

    get productIssueOutcomeOptions(){
        if(this.data.claimProductFinalOutcome == 'Returned to Customer' && this.data.claimProductFinalOutcome != undefined){
            return [
                {
                    label: 'Item Returned',
                    value: 'Item Returned'
                }
            ];
        }
        let opts  =  [
            {
                label:'Replacement Item',
                value:'Replacement Item'
            }
        ];

        let replacementPartOpt = {
            label:'Replacement Part',
            value:'Replacement Part'
        };
        let storeCreditOpt =
            {
                label:'Store Credit',
                value:'Store Credit'
            };
        let moneyBackOpt = {
                label:'Money Back (Major Fault Only)',
                value:'Money Back'
            };
        let itemReturnedOpt = {
                label:'Item Returned',
                value:'Item Returned'
            };
        

        // logic
        if(this.data != undefined && this.data.willSparePartCanFixIssue != undefined && this.data.isSpareAvailableInTable != undefined  && (
                ( this.data.willSparePartCanFixIssue == 'Yes' && this.data.isSpareAvailableInTable == 'Yes')
                || 
                (this.data.isSpareAvailableInTable == 'No' && this.data.assesmentPossible == 'Now'))) {
            opts.push(replacementPartOpt);
        }
        opts.push(storeCreditOpt);
        opts.push(moneyBackOpt);
       /* if(this.data.claimProductFinalOutcome == 'Returned to Customer' && this.data.claimProductFinalOutcome != undefined){
            opts.push(itemReturnedOpt);
        }*/
      

        return opts;
    }
   
   
    cpReasonOptions = [
        {"label": "ADVERTISED LOWER PRICE", "value": "ADVERTISED LOWER PRICE"},
        {"label": "CHANGE OF MIND", "value": "CHANGE OF MIND"},
        {"label": "CUSTOMER NOT AWARE OF PRE-ORDER DATE", "value": "CUSTOMER NOT AWARE OF PRE-ORDER DATE"},
        {"label": "DAMAGED IN TRANSIT", "value": "DAMAGED IN TRANSIT"},
        {"label": "DISCONTINUED PRODUCT", "value": "DISCONTINUED PRODUCT"},
        {"label": "DUPLICATE ORDER", "value": "DUPLICATE ORDER"},
        {"label": "FREEBIES", "value": "FREEBIES"},
        {"label": "LOST IN TRANSIT - CONFIRMED", "value": "LOST IN TRANSIT - CONFIRMED"},
        {"label": "LOST IN TRANSIT - PRESUMED DUE TO NO SCANNING", "value": "LOST IN TRANSIT - PRESUMED DUE TO NO SCANNING"},
        {"label": "PARTS MISSING", "value": "PARTS MISSING"},
        {"label": "PRODUCT FAULT", "value": "PRODUCT FAULT"},
        {"label": "PRODUCT NOT AS DESCRIBED", "value": "PRODUCT NOT AS DESCRIBED"},
        {"label": "PRODUCT NOT DISPATCHED OR DISPATCHED LATE OVER 48 HRS", "value": "PRODUCT NOT DISPATCHED OR DISPATCHED LATE OVER 48 HRS"},
        {"label": "PRODUCT RECALL", "value": "PRODUCT RECALL"},
        {"label": "RTS", "value": "RTS"},
        {"label": "WRONG ADDRESS", "value": "WRONG ADDRESS"},
        {"label": "WRONG PRODUCT DISPATCHED", "value": "WRONG PRODUCT DISPATCHED"},
        {"label": "WRONG PRODUCT QTY DISPATCHED", "value": "WRONG PRODUCT QTY DISPATCHED"},
        {"label": "WRONG SKU", "value": "WRONG SKU"},
        {"label": "ASSOCIATED PRODUCT", "value": "ASSOCIATED PRODUCT"}
    ]
    

    get cpAssessmentOutcomeOptions(){
        if(this.data && this.data.assessmentTableData && this.data.assessmentTableData.length > 0) {
            for(let temp of this.data.assessmentTableData) {
                if(temp.Assessment_Outcome__c == 'Fail') {
                    this.data.assessmentOutcome = 'Faulty';
                    this.disabledOutcome = true;
                    return [
                        {"label": "Faulty", "value": "Faulty"},
                        {"label": "Goodwill", "value": "Goodwill"},
                        {"label": "No Fault Found", "value": "No Fault Found"}
                    ]; 
                }
            }
            this.disabledOutcome = false;
            if(this.data.assessmentOutcome == 'Faulty'){
                this.data.assessmentOutcome = '';
            }
            return [
                {"label": "Goodwill", "value": "Goodwill"},
                {"label": "No Fault Found", "value": "No Fault Found"}
            ]; 
        }
        this.disabledOutcome = false;
        return [
            {"label": "Faulty", "value": "Faulty"},
            {"label": "Goodwill", "value": "Goodwill"},
            {"label": "No Fault Found", "value": "No Fault Found"}
        ]; 
    }  
    
    
    get hasNonDraftAssessmentForm() {
        if(!this.data.assessmentFormRequired){
            return true;
        }
        if(this.data && this.data.assessmentTableData && this.data.assessmentTableData.length > 0) {
            for(let temp of this.data.assessmentTableData) {
                if(temp.Assessment_Outcome__c != 'Draft' && !this.approvalScreen) {
                    return true;
                }
            }
        }else if(!this.data.assessmentTableData || this.data.assessmentTableData.length == 0){
            return false;
        }
        return false;
    }

    get hasNonDraftAssessmentForm2() {
        if(!this.data.assessmentFormRequired){
            return true;
        }
        if(this.data && this.data.assessmentTableData && this.data.assessmentTableData.length > 0) {
            for(let temp of this.data.assessmentTableData) {
                if(temp.Assessment_Outcome__c != 'Draft') {
                    return true;
                }
            }
        }else if(!this.data.assessmentTableData || this.data.assessmentTableData.length == 0){
            return false;
        }
        return false;
    }

    get showAssessmentFinalDetails(){
        return this.hasNonDraftAssessmentForm2 || !this.data.isBattrey || this.isNewAssessmentFormVisible == false;
    }

    assesmentPossibleOptions = [
        {"label": "Now", "value": "Now"},
        {"label": "Later", "value": "Later"}
    ]

    assessmentFaultCategoryOptions = [
        // {"label": "Electrical Issue", "value": "Electrical Issue"},
        // {"label": "Damaged Component", "value": "Damaged Component"}
    ]


    returnMethodOptions = [
        {
            label:'Customer Returns',
            value:'Customer Returns'
        },
        {
            label:'Prepaid Consignment',
            value:'Prepaid Consignment'
        },
        {
            label:'In Store',
            value: 'In Store'
        }
    ]


    claimProductItemLocationOptions = [
        {
            label:'Awaiting Return',
            value:'Awaiting Return'
        },
        {
            label:'At Store',
            value:'At Store'
        },
        {
            label:'In Transit to DC',
            value: 'In Transit to DC'
        },
        {
            label:'At DC',
            value: 'At DC'
        }
    ]

    dcReturnLocationOptions = [
        { label: 'Brisbane DC', value: 'Brisbane DC' },
        { label: 'Melbourne DC', value: 'Dandenong South DC' },
        { label: 'Perth DC', value: 'Perth DC' },
        { label: 'Sydney DC', value: 'Sydney DC' },
        { label: 'Townsville DC', value: 'Townsville DC' }
    ];


    @api validateInput() {
        // let valid  = true;
        let firstInvalidInput = null;

        let valid = [...this.template.querySelectorAll("lightning-input"), ...this.template.querySelectorAll("lightning-combobox")].reduce(
            (validSoFar, input) => {


                if (input.dataset.fieldlabel === "claimProductFinalOutcome" && (this.data.addBackToStockItems == undefined || this.data.addBackToStockItems.length == 0 ) && this.data.claimProductFinalOutcome == 'Add Back To Stock (Parts)' ) {
                    input.setCustomValidity("No Parts Available to Add back to Stock, Please select any other option");
                } else {
                    input.setCustomValidity(""); // Clear any custom validation messages
                }
                
                if(input.dataset.fieldlabel == 'Enter Batch Number' ) {
                    if (this.data.isBatchNumberCorrect == false ) {
                        input.setCustomValidity("Please enter a valid batch number.");
                    } else {
                        input.setCustomValidity(""); // Clear any custom validation messages
                    }
                }

                if(input.checkValidity()) {
                    input.classList.add("slds-has-error");
                    // Store the first invalid input to scroll to it later
                    if (!firstInvalidInput) {
                        firstInvalidInput = input;
                    }
                } else {
                    input.classList.remove("slds-has-error");
                }

                input.reportValidity();
                return validSoFar && input.checkValidity();
            },
            true
        )
        

        let childValid = true;
        if(this.requireFileUploadCheck && this.template.querySelectorAll("c-claim-form-file-upload") != undefined) {
            childValid = [...this.template.querySelectorAll("c-claim-form-file-upload")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }
        console.log('childValid -> ' + JSON.stringify(childValid, null, 2));

        let claimFormcpItemProductSelectionValid = true;
        if(this.template.querySelectorAll("c-claim-form-c-p-item-product-selection") != undefined) {
            claimFormcpItemProductSelectionValid = [...this.template.querySelectorAll("c-claim-form-c-p-item-product-selection")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }

        let returOrderValid = true;
        if(this.template.querySelectorAll("c-claimform-return-order") != undefined) {
            returOrderValid = [...this.template.querySelectorAll("c-claimform-return-order")].reduce(
                (validSoFar, input) => {
                    return validSoFar && input.validateInput();
                },
                true
            )
        }

        /*let reusuableLookupValid = true;
        if (this.template.querySelectorAll("c-reusuable-lookup") != undefined) {
            reusuableLookupValid = [...this.template.querySelectorAll("c-reusuable-lookup")].reduce(
                (validSoFar, input) => {
                    let returnObj = input.validate();

                    return validSoFar && returnObj?.isValid;
                },
                true
            )
        }
        console.log('reusuableLookupValid -> ' + JSON.stringify(reusuableLookupValid, null, 2));*/
        
        //return valid && childValid && claimFormcpItemProductSelectionValid && returOrderValid && reusuableLookupValid;
        return valid && childValid && claimFormcpItemProductSelectionValid && returOrderValid;
        
    }


    @wire(getContentDocumentIdsBySKU, { productSKU: '$data.sku' })
    wiredContentDocumentIds({ error, data }) {
        if (data) {
            console.log('wiredContentDocumentIds -> ' + JSON.stringify(data));
            if(data?.batchLocation) {
                this.batchPhotoLocationContentDocumentId = `/sfc/servlet.shepherd/document/download/${data.batchLocation}`;
                this.showPreviewBatchPhoto = true;
            } else {
                this.showPreviewBatchPhoto = false;
            }

            if(data?.serialLocation) {
                this.serialPhotoLocationContentDocumentId = `/sfc/servlet.shepherd/document/download/${data.serialLocation}`;
                this.showPreviewSerialPhoto = true;
            } else {
                this.showPreviewSerialPhoto = false;
            }

           
        } else if (error) {
            console.error('Error fetching content document ids:', error);
        }
    }

    @wire(getCategoriesBySKU, { sku: '$data.sku' })
    wiredcategories({ error, data }) {
        if (data) {
            console.log('getCategoriesBySKU -> ' + JSON.stringify(data));
            let existingCats = [];
            let assessmentFaultCategories = [];
            
            // Process the data into options and categories
            data.forEach(element => {
                let catObj = element.Assessment_Fault_Category__r;
                let pickObj = { label: catObj.Assessment_Fault_Category_Name__c, value: element.Assessment_Fault_Category__c };
                existingCats.push(pickObj);
                assessmentFaultCategories.push(catObj);
            });
    
            // Set the assessmentFaultCategories regardless of 'Other'
            this.assessmentFaultCategories = JSON.parse(JSON.stringify(assessmentFaultCategories));
    
            // Check if 'Other' is already in the options
            const hasOther = existingCats.some(cat => cat.label === 'Other');
            if (!hasOther) {
                // If 'Other' is not present, fetch its ID and add it
                getOtherAssessmentId()
                    .then(result => {
                        let pickObj = { label: 'Other', value: result };
                        existingCats.push(pickObj);
                        this.assessmentFaultCategoryOptions = JSON.parse(JSON.stringify(existingCats));
                    })
                    .catch(error => {
                        console.log(error);
                    });
            } else {
                // If 'Other' is already present, set the options directly
                this.assessmentFaultCategoryOptions = JSON.parse(JSON.stringify(existingCats));
            }
    
            console.log(this.assessmentFaultCategoryOptions);
            console.log(this.assessmentFaultCategories);
        } else if (error) {
            console.error('Error fetching assessment fault categories:', error);
        }
    }

    /**
     * Check if current user's location is allowed for Assessment Form
     */
    checkUserLocationForAssessmentForm() {
        if (this.userLocation) {
            isUserLocationAllowedForAssessmentForm({ userLocation: this.userLocation })
                .then(result => {
                    this.isNewAssessmentFormVisible = result;
                    console.log('Assessment Form visibility check result -> ' + result);
                })
                .catch(error => {
                    console.error('Error checking user location for Assessment Form:', error);
                    this.isNewAssessmentFormVisible = false; // Default to false on error
                });
        } else {
            this.isNewAssessmentFormVisible = false;
        }
    }


    /*
     * No Parameters.
     * This wire syntax is for the retrieval of the current user's 4WD Location and user data
    */
    @wire(getRecord, { recordId: '$userID', fields: [USER_4WD_LOCATION_FIELD, USER_PROFILE_NAME, USER_NAME]})
    getUserRecordData({ error, data }) {
        if (data) {
            this.userProfileName = getFieldValue(data, USER_PROFILE_NAME);
            this.userLocation = getFieldValue(data, USER_4WD_LOCATION_FIELD);

            // Call Apex method to check if user location is allowed for Assessment Form
            this.checkUserLocationForAssessmentForm();

            console.log('this.userLocation -> ' + this.userLocation);
            console.log('this.isNewAssessmentFormVisible -> ' + this.isNewAssessmentFormVisible);
            
            this.userDefaultAddBackToStockLocation = this.userLocation.replace(/_/g, " ");
            console.log('this.userDefaultAddBackToStockLocation -> ' + JSON.stringify(this.userDefaultAddBackToStockLocation, null, 2));

            if(!this.userLocation.includes(';')) {
                if (this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') {
                    this.isAddBackToStockLocationChangeDisabled = true;
                } else if (this.userProfileName == 'Warehouse' && (this.userLocation != undefined && this.userLocation.endsWith("DC"))) {
                    this.isAddBackToStockLocationChangeDisabled = true;
                }
                else {
                    this.isAddBackToStockLocationChangeDisabled = false;
                }
            } else {
                this.addBackToStockActionDisabled = false;
                this.isAddBackToStockLocationChangeDisabled = false;
            }
        } else if (error) {
            console.error('Error fetching Returned Location picklist values', error);
        }
    }
    

    /*
     * No Parameters.
     * Retrieval of Shipping Location Records.
    */
    @wire(getShippingLocationList, { userLocation: '$userLocation' })
    getShippingLocationList({ data, error }) {
        if (data) {
            try{
                const arrayToObject1 = (arr, key) => {
                    return arr.reduce((obj, item) => {
                        obj[item[key]] = item;
                        return obj;
                    },{})
                };

                this.shippingLocationList = arrayToObject1(data, 'Id');
                let shippingAddress = JSON.parse(JSON.stringify(this.erpData.shipping_address));
                let orderShippingCity = shippingAddress.suburb;

                let options = [];
                for (const d of data) {
                    if((this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') ) {
                        console.log('going to if for store');
                        if(this.userLocation != undefined && this.userLocation != '') {
                            this.shippingLocationSelected = this.getIdByCity(this.shippingLocationList, this.userLocation);
                            console.log('shippingLocationSelected@@@@'+this.shippingLocationSelected);
                            this.locationValue = this.shippingLocationSelected;
                            console.log('locationValue@@@@'+this.locationValue);
                        } else{
                            this.shippingLocationSelected = '';
                        }

                    } else {
                        console.log('going to esle for admin');
                        if(orderShippingCity == d.City__c) {
                            this.shippingLocationSelected = d.Id;
                            this.locationValue = d.Id;
                            let shipToAddress = this.shipToAddress;
                            let shippingLocationAddress = this.shippingLocationList[this.shippingLocationSelected];
                            if (shippingLocationAddress) {
                                shipToAddress.street = [shippingLocationAddress.Pickup_Label__c + '\n' + shippingLocationAddress.Street__c];
                                shipToAddress.suburb = shippingLocationAddress.City__c;
                                shipToAddress.state = shippingLocationAddress.State__c;
                                shipToAddress.postcode = shippingLocationAddress.Postal_Code__c;
                                shipToAddress.country_code = shippingLocationAddress.Country__c;
                            }
                            console.log('402 shipToAddress -> ' + JSON.stringify(shipToAddress, null, 2));
                            this.shipToAddress = shipToAddress;
                            this.defaultAddressAdded = true;
                            this.data.shipToAddress = JSON.parse(JSON.stringify(this.shipToAddress));
                        }

                        if (d.Shipping_Method_API_Name__c == 'standard_shipping') {
                            this.standardShippingLocationId = d.Id;
                            // this.shippingLocationSelected = d.Id;
                        }
                    }

                    options.push({
                        'label': d.Name,
                        'value': d.Id
                    });
                }
                if(this.shippingLocationSelected == '' || this.shippingLocationSelected == undefined) {
                    this.shippingLocationSelected = this.standardShippingLocationId;
                }

                let ship_method_label = 'Standard Shipping';
                // Find and remove the Standard Shipping option
                let standardShippingIndex = options.findIndex(item => item.label === ship_method_label);
                let standardShippingOption = standardShippingIndex !== -1 ? options.splice(standardShippingIndex, 1)[0] : null;
                options.sort((a, b) => a.label.localeCompare(b.label));

                if (standardShippingOption) {
                    options.unshift(standardShippingOption);
                }


                this.shippingLocationOptions = options;    

                if(this.shippingLocationSelected == this.standardShippingLocationId) {
                    this.shipToAddress = JSON.parse(JSON.stringify(this.defaultShipToAddress));
                    this.locationValue = '';
                } else {
                    let shipToAddress = this.shipToAddress;
                    let shippingLocationAddress = this.shippingLocationList[this.shippingLocationSelected];
                    if (shippingLocationAddress) {
                        shipToAddress.street = [shippingLocationAddress.Pickup_Label__c + '\n' + shippingLocationAddress.Street__c];
                        shipToAddress.suburb = shippingLocationAddress.City__c;
                        shipToAddress.state = shippingLocationAddress.State__c;
                        shipToAddress.postcode = shippingLocationAddress.Postal_Code__c;
                        shipToAddress.country_code = shippingLocationAddress.Country__c;
                    }
                    this.shipToAddress = shipToAddress;
                    console.log('shipToAddress@@@@@@'+shipToAddress);
                    this.shippingLocationUpdatedByCustomLogic = true;
                }
            }catch(e){}
        } else if (error) {

            let formattedError = ldsUtils.reduceErrors(error);
            this.message = formattedError;
            this.displayError = true;

            this.isLoading = false;

        }

    }

    /*
     * No Parameters.
     * Retrieval of Active Couriers.
    */
    @wire(getActiveCouriers)
       getActiveCouriers({ data, error }) {
           if (data) {
   
               let options = [];
               let newMap = {};
   
               for (let i = 0; i < data.length; i++) {
   
                   options.push({
                       'label': data[i].Name,
                       'value': data[i].Id
                   });
   
                   newMap[data[i].Id] = data[i];
   
               }
   
               this.courierOptions = options;
               this.courierMap = newMap;
   
           } else if (error) {
   
               let formattedError = ldsUtils.reduceErrors(error);
               this.showError(formattedError);
   
           }
    }
    
    /*
     * No Parameters.
     * Retrieval of Shipping To Address.
    */
    @wire(getShipToAddress)
    getShipToAddress({ data, error }) {

        if (data) {
            this.formatShipToAddress(data);
        } else if (error) {

            let formattedError = ldsUtils.reduceErrors(error);
            this.showError(formattedError);

        }

    }

    @wire(getRecordTypes, { objectName: 'Assessment_Form__c' })
    wiredRecordTypes({ error, data }) {
        if (data) {
            this.assessmentRecordTypes = data;
            this.error = undefined;
        } else if (error) {
            this.error = error;
            this.assessmentRecordTypes = undefined;
        }
    }

    connectedCallback() {
        console.log('sfCaseID@@@'+this.sfCaseID);
        if (this.sfCaseID) {
            isCreatedByGuestUser({ caseId: this.sfCaseID })
                .then(result => {
                    this.isGuest = result;
                    console.log('Is created by guest user:', result);
                })
                .catch(error => {
                    console.error('Error checking guest user:', error);
                });
        }
    
        console.log('productIssueChild this.data -> ' + JSON.stringify(this.data, null, 2));
        setTimeout(() => {
            if(this.approvalScreen == true) {
                this.checkForClaimProductActions();
            }
        }, 1000);
    }

    @api checkForClaimProductActions() {
        console.log('calling inside@@@');
        let dataLocal = JSON.parse(JSON.stringify(this.data));
        
        if(this.isIssueCategoryIsChangeOfMind && !this.isOutOfChangeOfMindDuration && this.COMAdvertisedAtALowerPrice && this.has48HourPassedSinceShipped != false) {
            dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card');
        } else if(this.isIssueCategoryIsChangeOfMind && !this.isOutOfChangeOfMindDuration && this.COMProductNotAsDescribedORCOMDoesNotSuitCustomerNeeds) {
            dataLocal = this.createClaimProductAction(dataLocal, 'Add Back to Stock');
            dataLocal = this.createClaimProductAction(dataLocal, 'Gift Card');
        }
        this.data = JSON.parse(JSON.stringify(dataLocal));

        this.updateDataObjToParent();
    }


    createClaimProductAction(dataLocal, actionType) {
        //Check for exisitng claim product action
        let claimProductAction = {}; 
        let indexOfCPA = 0;
        
        if(dataLocal.claimProductActions != undefined && dataLocal.claimProductActions != []) {
            console.log('coming inisde claimProductActions@@@@@@');
            indexOfCPA = dataLocal.claimProductActions.findIndex(record => record['Action_Type__c'] === actionType);
            claimProductAction = dataLocal.claimProductActions[indexOfCPA];
        } else {
            dataLocal.claimProductActions = [];
        }
        
        let dataLocalTempCopy = JSON.parse(JSON.stringify(dataLocal));
        if(dataLocalTempCopy.claimProductActions) {
            delete dataLocalTempCopy.claimProductActions;
        }
        claimProductAction.Action_Type__c = actionType;
        claimProductAction.Status__c = 'Pending';
        claimProductAction.JSON__c = dataLocalTempCopy;

        dataLocal.claimProductActions[indexOfCPA] = claimProductAction;

        return dataLocal;
    }

    /*
     * Parameters
        * event - onchange event from the lightning input and lightning combobox components.
     * Assignment of serialBatchValue, selectedResolution, and isValid properties on the cardData variable.
    */
    handleChange(event) {
      
        let id = event.target.dataset.id.substring(0, event.target.dataset.id.indexOf(';'));
        let type = event.target.dataset.type;
        let dataLocal = JSON.parse(JSON.stringify(this.data));
        let enteredValue = event.target.value;

        console.log('in item handle change');                        
        if (type == 'input') { 
            if(event.target.dataset.fieldlabel == 'Enter Serial Number') {
                dataLocal.serialValue = event.target.value;
            } else if(event.target.dataset.fieldlabel == 'Enter Batch Number') {
                dataLocal.batchValue = event.target.value;
                console.log('calling apex here');
        
        
                clearTimeout(this.batchTypingTimer);
                if(dataLocal.batchValue == '' || dataLocal.batchValue == undefined) {
                    dataLocal.isBatchNumberCorrect = true;
                    let inputField = this.template.querySelector(`lightning-input[data-fieldlabel="${dataLocal.batchLabel}"]`);
                    inputField.setCustomValidity('');
                    dataLocal.isBatchNumberCorrect = true;
                } else {
                    this.batchTypingTimer = setTimeout(() => {
                    // Call Apex method to validate batch number
                    
                    isValidBatchNumberApex({ batchNumber: this.data.batchValue+'', skuString: dataLocal.sku })
                    .then(result => {
                        let inputField = this.template.querySelector(`lightning-input[data-fieldlabel="${dataLocal.batchLabel}"]`);
                        console.log('isValidBatchNumberApex result -> ' + result);
                        if (!result) {
                            inputField.setCustomValidity('Please enter a valid batch number.');
                            dataLocal.isBatchNumberCorrect = false;
                        } else {
                            inputField.setCustomValidity('');
                            dataLocal.isBatchNumberCorrect = true;
                        }

                        inputField.reportValidity();
                        

                        this.data = JSON.parse(JSON.stringify(dataLocal));
                        this.updateDataObjToParent();
                    })
                    .catch(error => {
                        console.error('Error calling Apex method isValidBatchNumberApex:', error);
                    });
                    }, this.doneTypingInterval);
                }
                
            } else {
                dataLocal[event.target.dataset.fieldlabel] = event.target.value;
            }
            let fieldLabel = event.target.dataset.fieldlabel;
            if(fieldLabel == 'itemPrice' || fieldLabel == 'priceAdvertised') {
                if(dataLocal['itemPrice'] != undefined && dataLocal['priceAdvertised'] != undefined) {
                    let priceAdvertised = parseFloat(dataLocal['priceAdvertised']);
                    if (isNaN(priceAdvertised) || priceAdvertised === 0) {
                        dataLocal['creditDueAmount'] = 0;
                    } else {
                        // Calculate credit due amount
                        let creditDueAmount = dataLocal['itemPrice'] - priceAdvertised;
                        // Restrict to 2 decimal places
                        creditDueAmount = parseFloat(creditDueAmount.toFixed(2));
                        dataLocal['creditDueAmount'] = creditDueAmount;
                    }
                } else {
                    dataLocal['creditDueAmount'];
                }
            } 
            // else if(fieldLabel == 'assessmentFaultDescription') {
            //     if(event.target.value == '') {
            //         this.isAssessmentFaultDescriptionDisabled = true;
            //     } else {
            //         this.isAssessmentFaultDescriptionDisabled = false;
            //     }
            // }
        } else if (type == 'combobox') {
            if(event.target.dataset.fieldlabel) {
                dataLocal[event.target.dataset.fieldlabel] = event.target.value;
                if(event.target.dataset.fieldlabel == 'selectedIssueCategory') {
                    dataLocal['selectedIssueSubCategory'] = '';
                    dataLocal['hasApprovalStatus'] = false;
                    dataLocal['isPendingAssessment'] = false;
                }

               

                if(event.target.dataset.fieldlabel == 'selectedIssueCategory' || event.target.dataset.fieldlabel == 'selectedIssueSubCategory' ) {
                    dataLocal['uploadedFiles'] = [];
                }

                if(event.target.dataset.fieldlabel == 'assesmentPossible') {
                    dataLocal['assessmentFaultCategory'] = '';
                    dataLocal['assessmentFaultDescription'] = '';
                    dataLocal['assessmentOutcome'] = '';
                    dataLocal['assessmentStaffName'] = '';
                    dataLocal['claimProductFinalOutcome'] = '';
                    dataLocal['claimProductItemLocation'] = '';
                    dataLocal['assessmentTableData'] = [];
                    if(dataLocal['willSparePartCanFixIssue'] != 'Yes') {
                        dataLocal['resolutionOutcome'] = '';
                    }

                    dataLocal['replacements'] = [];
                    if(event.target.value == 'Later') {
                        dataLocal['hasApprovalStatus'] = true;
                        dataLocal['isPendingAssessment'] = true;
                        if(this.userProfileName != 'Store User' && this.userProfileName != 'Store Manager') {
                            dataLocal['productReturnAction'] = 'Yes';
                        }
                        else{
                            //dataLocal['productReturnAction'] = 'No';
                        }
                    } else {
                        dataLocal['hasApprovalStatus'] = false;
                        dataLocal['isPendingAssessment'] = false;
                    }
                } 

                if(event.target.dataset.fieldlabel == 'assessmentOutcome') {
                    let failedOutcomes = ['Faulty', 'Goodwill','No Fault Found'];
                    if(failedOutcomes.includes(event.target.value)) {
                        dataLocal['claimProductFinalOutcome'] = '';
                    }
                    if(event.target.value == 'No Fault Found') {
                        dataLocal['resolutionOutcome'] = 'Item Returned';
                        dataLocal['claimProductFinalOutcome'] = 'Returned to Customer';
                    }
                }
                

                if(event.target.dataset.fieldlabel == 'selectedIssueCategory' && event.target.value == 'Pre Order Date') {
                    if(this.isItemDispatched == true) {
                        dataLocal['Approval_Status__c'] = 'Rejected';
                        dataLocal['Status__c'] = 'Closed';

                        dataLocal['OrderStatus'];
                    } else {
                        dataLocal['OrderStatus'] = 'Cancelled Order';

                        dataLocal['Approval_Status__c'];
                        dataLocal['Status__c'];
                    }
                    
                }

                if(event.target.dataset.fieldlabel == 'shippingTo') {
                    this.data.shippingToName = event.target.options.find(opt => opt.value === event.detail.value).label;
                    dataLocal = this.filterShipToAddress(event.detail.value);
                }

                if(event.target.dataset.fieldlabel == 'Courier') {
                    dataLocal.courierName = event.target.options.find(opt => opt.value === event.detail.value).label;
                }

                if(event.target.dataset.fieldlabel == 'claimProductFinalOutcome' && (event.detail.value == 'Add Back To Stock (Seconds)' || event.detail.value == 'Add Back To Stock' || event.detail.value == 'Add Back To Stock (Parts)') && this.sfClaimProductActionID != undefined ){
                    dataLocal.userLocation = this.userLocation;
                    dataLocal.userLocationName = this.userLocationName;
                    dataLocal.addBackToStockActionLocation = dataLocal.userLocationName;
                }

                if(event.target.dataset.fieldlabel == 'claimProductFinalOutcome' && event.detail.value != 'Product Disposed') {
                    dataLocal.returnDCLocation = '';
                }
                
                if(event.target.dataset.fieldlabel == 'assessmentFaultCategory') {
                    const selectedValue = event.detail.value;

                    // Find the corresponding label based on the selected value
                    const selectedOption = this.assessmentFaultCategoryOptions.find(option => option.value === selectedValue);
            
                    // If the option is found, set the label
                    if (selectedOption) {
                        dataLocal.assessmentFaultCategoryLable = selectedOption.label;
                    }
                }

                if (event.target.dataset.fieldlabel == 'resolutionOutcome'  && event.detail.value =='Replacement Item' && (this.userProfileName == 'Store Manager' || this.userProfileName == 'Store User')) {
                    dataLocal.shippingMethod = 'Pick Up';
                }

                if (event.target.dataset.fieldlabel == 'resolutionOutcome'  && event.detail.value =='Money Back') {
                    dataLocal.refundType = this.erpData.refund_type != undefined ? this.erpData.refund_type == 'manual' ? 'Manual' : this.erpData.refund_type : 'Manual';
                    dataLocal.caseType = this.erpData.store_name == 'WHOLESALE ACCOUNTS' ? 'Commercial' :
                        this.erpData.store_name == '4WD SUPACENTRE (CAMPER TRAILERS)' || this.erpData.order_type == 'Camper Trailers' ?
                            'Camper Trailers' : 'Claims Department';
                    dataLocal.salesChannel = this.erpData.sales_channel ? this.erpData.sales_channel : '';
                    dataLocal.refundMethod = dataLocal.caseType == 'Commercial' ? 'Invoice Credit' :
                        dataLocal.refundType == 'integration' || dataLocal.salesChannel == 'Camper_Trailers' ? 'Integration' : 'Bank Account';
        
                    // this.updateDataObjToParent();
                }

                if(event.target.dataset.fieldlabel == 'userLocation') {
                    // dataLocal.userLocation = event.detail.value;
                    dataLocal.userLocationName = event.target.options.find(opt => opt.value === event.detail.value).label;
                    dataLocal.addBackToStockActionLocation = dataLocal.userLocationName;
                }

            } else {
                dataLocal.selectedResolution = event.detail.value;
            }
        } else if (type === 'checkbox') {
            dataLocal[event.target.dataset.fieldlabel] = event.target.checked;
        }
        if(event.target.dataset.fieldlabel == 'Assessment Outcome' && event.detail.value == 'No Fault Found'){
            dataLocal.claimProductFinalOutcome = 'Returned to Customer';
            dataLocal.resolutionOutcome = 'Item Returned';
        }
        if(event.target.dataset.fieldlabel == 'claimProductFinalOutcome'  && event.detail.value =='Returned to Customer') {
            dataLocal.resolutionOutcome = 'Item Returned';
        }
        // if (this.validateSerialBatchValues(cardData[id]) && cardData[id].selectedResolution && (cardData[id].uploadedFiles.length > 0 || this.isCamper)) {
        //     cardData[id].isValid = true;
        // } else if (this.validateSerialBatchValues(cardData[id]) && cardData[id].selectedResolution && (cardData[id].uploadedFiles.length > 0 || this.isCamper)) {
        //     cardData[id].isValid = true;
        // } else {
        //     cardData[id].isValid = false;
        // }

        // this.cardData = cardData;
        // this.data = this.cardData[this.currentItemIndex];
        this.data = JSON.parse(JSON.stringify(dataLocal));

        clearTimeout(this.typingTimer);
        this.typingTimer = setTimeout(() => {
            this.updateDataObjToParent();
        }, this.doneTypingInterval);
        
    }

    handleRPSelection(event) {
        this.data.Return_Pallet__c = event.detail.id;
        this.data.Return_Pallet_Name = event.detail.mainField;
      
        this.updateDataObjToParent();
    }

    handleRPRemoval(event) {
        this.data.Return_Pallet__c = '';
        this.data.Return_Pallet_Name = '';
        
        this.updateDataObjToParent();
    }

    handleNewReturnPallet() {
        this.data.Return_Pallet__c = '';
        this.data.Return_Pallet_Name = '';

        this.showAddReturnPalletModal = true;
    }

    closeReturnPalletFormModal() {
        this.showAddReturnPalletModal = false;
    }

    saveReturnPalletForm(event) {
        if(!this.validateReturnPalletForm()) { return false;}

        event.preventDefault();       // stop the form from submitting
        const fields = event.detail.fields;
        this.template.querySelector('lightning-record-edit-form').submit(fields);
        
        this.showAddReturnPalletModal = false;
    }
    
    handleReturnPalletSuccess(event) {
        console.log('in handleReturnPalletSuccess');
        
        getReturnPalletInfo({ returnPalletId: event.detail.id}) 
        .then(result => {
            console.log('getReturnPalletInfo result -> ' + JSON.stringify(result, null, 2));
            let dataLocal = JSON.parse(JSON.stringify(this.data));

            dataLocal.Return_Pallet__c = result.Id;
            dataLocal.Return_Pallet_Name = result.Name;
            
            console.log('1266 dataLocal -> ' + JSON.stringify(dataLocal, null, 2));
            
            this.data = JSON.parse(JSON.stringify(dataLocal));
            this.updateDataObjToParent();
        })      
        .catch(error => {
            console.log('error -> ' + JSON.stringify(error, null, 2));
        })
        //this.data.Return_Pallet__c = event.detail.id;

        //this.updateDataObjToParent();
    }

    validateBatchNumber(enteredValue, dataLocal) {
        isValidBatchNumberApex({ batchNumber: enteredValue+'', skuString: dataLocal.sku })
        .then(result => {
            let inputField = this.template.querySelector(`lightning-input[data-fieldlabel="${dataLocal.batchLabel}"]`);
            if(inputField) {
                if (!result) {
                    inputField.setCustomValidity('Please enter a valid batch number.');
                    dataLocal.isBatchNumberCorrect = false;
                } else {
                    inputField.setCustomValidity('');
                    dataLocal.isBatchNumberCorrect = true;
                }

                inputField.reportValidity();
            }
            
        })
        .catch(error => {
            console.error('Error calling Apex method isValidBatchNumberApex:', error);
        });
    }
     
    /*
     * Parameters
        * data - list of shipping to address.
     * Formats the Shipping To field.
    */
    formatShipToAddress(data) {

        let shippingToOptions = [];
        let shippingToList = [];

        if (data.length > 0) {

            for (let i = 0; i < data.length; i++) {

                shippingToOptions.push({
                    'label': data[i].MasterLabel,
                    'value': data[i].DeveloperName,
                });

                shippingToList.push({
                    'id': data[i].DeveloperName,
                    'label': data[i].MasterLabel,
                    'buildingName': data[i].Building_Name__c,
                    'street': data[i].Street__c,
                    'state': data[i].State__c,
                    'city': data[i].City__c,
                    'postcode': data[i].Postal_Code__c,
                    'country': data[i].Country__c,
                    'firstName': data[i].First_Name__c,
                    'lastName': data[i].Last_Name__c,
                    'email': data[i].Email__c,
                    'phone': data[i].Phone__c
                });

            }

        }

        this.shippingToOptions = shippingToOptions;
        this.shippingToList = shippingToList;

        this.filterShipToAddress(this.shippingToValue);

    }

    /* Parameters
        * value - selected ship to field.
    * Formats the Shipping To field.
    */
    filterShipToAddress(value) {
        let dataLocal = JSON.parse(JSON.stringify(this.data));
        let shippingToList = JSON.parse(JSON.stringify(this.shippingToList));
        console.log('shippingToList -> ' + JSON.stringify(shippingToList, null, 2));
        let filteredList = shippingToList.filter(function (el) {
            return el.id == value;
        });
        console.log('filteredList -> ' + JSON.stringify(filteredList, null, 2));

        if (filteredList.length > 0) {

            dataLocal.shipToAddress = {
                'buildingName': filteredList[0].buildingName,
                'street': filteredList[0].street,
                'suburb': filteredList[0].city,
                'state': filteredList[0].state,
                'postcode': filteredList[0].postcode,
                'country_code': filteredList[0].country,
                'firstName': filteredList[0].firstName,
                'lastName': filteredList[0].lastName,
                'email': filteredList[0].email,
                'phone': filteredList[0].phone
            };

            this.shipToAddress = {...dataLocal.shipToAddress};

        }

        this.data = {...JSON.parse(JSON.stringify(dataLocal))};
        return dataLocal;
    }

   


    shipToAddOnChange(event) {
        let shipToAddress = this.shipToAddress;
        let street = [];

        street.push(event.target.street);

        shipToAddress.street = street;
        shipToAddress.suburb = event.target.city;
        shipToAddress.state = event.target.province;
        shipToAddress.postcode = event.target.postalCode;
        shipToAddress.country_code = event.target.country;

        // this.billToAddress.country_code = event.target.country;
        this.shipToAddress = {...shipToAddress};
        
        this.data.shipToAddress = JSON.parse(JSON.stringify(this.shipToAddress));
    }

    handleProductSelectionChange(event) {   
        console.log('in handleProductSelectionChange');
        console.log('event.detail -> ' + JSON.stringify(event.detail, null, 2));
        this.data.selectProds = event.detail.data;
        console.log(' this.data.selectProds -> ' + JSON.stringify( this.data.selectProds, null, 2));
        this.updateDataObjToParent();
    }

    handleDataChange(event) {
        console.log('handleDataChange', event);
        this.data = JSON.parse(JSON.stringify(event.detail.data));
        this.updateDataObjToParent();
    }

    handleOpenBatchPhotoLocationModel(event) {
        this.showOpenBatchPhotoLocationModel = true;
    }

    closePreview(event) {
        this.showOpenBatchPhotoLocationModel = false;
    }

    handleOpenSerialPhotoLocationModel(event) {
        this.showOpenSerialPhotoLocationModel = true;
    }

    closeSerialPreview(event) {
        this.showOpenSerialPhotoLocationModel = false;
    }

    openAssessmentFormModal() {
        this.showAddAssessmentFormModal = true;
    }

    closeAssessmentFormModal() {
        this.showAddAssessmentFormModal = false;
        // this.currentAssessmentForm = {};
    }

    handleAssessmentFormSaved(event) {
        let dataLocal = JSON.parse(JSON.stringify(this.data));
        let assessmentTableData = dataLocal.assessmentTableData != undefined ? dataLocal.assessmentTableData : [];
        let newRecord = JSON.parse(JSON.stringify(event.detail.assessmentData));
        if(newRecord.Assessment_Outcome__c == 'Draft'){
            dataLocal['hasApprovalStatus'] = true;
            dataLocal['isPendingAssessment'] = true;
        }else{
            dataLocal['hasApprovalStatus'] = false;
            dataLocal['isPendingAssessment'] = false;
        }
        if (this.currentAssessmentForm && this.currentAssessmentForm.uuid) {
            // Find the existing record and update it
            let existingRecordIndex = assessmentTableData.findIndex(record => record.uuid === this.currentAssessmentForm.uuid);
            if (existingRecordIndex !== -1) {
                newRecord.uuid = this.currentAssessmentForm.uuid;
                newRecord.SelectedType = this.isProductCategoryRefrigeration ? 'Refrigeration' :
                             this.isProductCategoryBushPower ? 'battery' : null;
                assessmentTableData[existingRecordIndex] = newRecord;
               newRecord[SELECTED_RECORD_TYPE_FIELD.fieldApiName] = this.isProductCategoryRefrigeration ? 'Refrigeration' :
                             this.isProductCategoryBushPower ? 'battery' : null;
                assessmentTableData[existingRecordIndex] = newRecord;

            }
        } else {
            // Create a new record
            newRecord.uuid = Math.random().toString(36).substring(2, 15); // Generate a random Id
            newRecord.SelectedType = this.isProductCategoryRefrigeration ? 'Refrigeration' :
                            this.isProductCategoryBushPower ? 'battery' : null;
            newRecord[SELECTED_RECORD_TYPE_FIELD.fieldApiName] = this.isProductCategoryRefrigeration ? 'Refrigeration' :
                            this.isProductCategoryBushPower ? 'battery' : null;
            assessmentTableData = [...assessmentTableData, newRecord];
        }

        dataLocal.assessmentTableData = assessmentTableData;
        this.data = JSON.parse(JSON.stringify(dataLocal));
        this.closeAssessmentFormModal();
        this.updateDataObjToParent();
    }

    saveAssessmentForm() {
        if(!this.validateAssessmentForm()) { return false;}

        let dataLocal = JSON.parse(JSON.stringify(this.data));
        let assessmentTableData = dataLocal.assessmentTableData != undefined ? dataLocal.assessmentTableData : [];
        const fields = this.template.querySelectorAll('lightning-input-field');
        let newRecord = {};
        let purchaseDate = null;
        let dateTimePutOnPower = null;
        let dateTimeRecorded = null;
        fields.forEach(field => {
          newRecord[field.fieldName] = field.value;

          if (field.fieldName === "Purchase_Date__c" && field.value != undefined && field.value != null) {
            purchaseDate = new Date(field.value);
          }
          if (field.fieldName === "Date_Time_Put_on_Power__c" && field.value != undefined && field.value != null) {
            dateTimePutOnPower = new Date(field.value);
          }
          if (field.fieldName === "Date_Time_Recorded__c" && field.value != undefined && field.value != null) {
            dateTimeRecorded = new Date(field.value);
          }
        });

        if (
            (dateTimePutOnPower != undefined && dateTimePutOnPower != null && purchaseDate != undefined && !this.validatePowerDatetime(purchaseDate, dateTimePutOnPower, dateTimeRecorded))
        ) {
            this.showError('Date/Time Put on Power must be equal or later than Purchase Date.');
            return;  
        }

        if((dateTimeRecorded != null && dateTimeRecorded != undefined && purchaseDate != undefined  && !this.validateRecordDatetime(purchaseDate, dateTimePutOnPower, dateTimeRecorded))) {
            this.showError('Date/Time Recorded must be equal or later than Date/Time Put on Power.');
            return;
        }
    
        if (this.currentAssessmentForm && this.currentAssessmentForm.uuid) {
            // Find the existing record and update it
            let existingRecordIndex = assessmentTableData.findIndex(record => record.uuid === this.currentAssessmentForm.uuid);
            if (existingRecordIndex !== -1) {
                newRecord.uuid = this.currentAssessmentForm.uuid;
                newRecord.SelectedType = this.isRefrigerationType ? 'Refrigeration' :
                             this.isbatteryType ? 'battery' : null;
                assessmentTableData[existingRecordIndex] = newRecord;
               newRecord[SELECTED_RECORD_TYPE_FIELD.fieldApiName] = this.isRefrigerationType ? 'Refrigeration' :
                             this.isbatteryType ? 'battery' : null;
                assessmentTableData[existingRecordIndex] = newRecord;

            }
        } else {
            // Create a new record
            newRecord.uuid = Math.random().toString(36).substring(2, 15); // Generate a random Id
            assessmentTableData = [...assessmentTableData, newRecord];
        }
    
        dataLocal.assessmentTableData = assessmentTableData;
        if (this.isRefrigerationType) {
            dataLocal.assessmentTableData.forEach(record => record.SelectedType = 'Refrigeration');
            //record[SELECTED_RECORD_TYPE_FIELD.fieldApiName] = 'Refrigeration';
        }
        if (this.isbatteryType) {
            dataLocal.assessmentTableData.forEach(record => record.SelectedType = 'battery');
            //record[SELECTED_RECORD_TYPE_FIELD.fieldApiName] = 'battery';
        }

        this.data = JSON.parse(JSON.stringify(dataLocal));
        this.closeAssessmentFormModal();
        this.updateDataObjToParent();
    }

    validatePowerDatetime(purchaseDate, powerDatetime, recordedDatetime) {

        const purchaseDateObj = new Date(purchaseDate);
        const powerDatetimeObj = new Date(powerDatetime);
    
        purchaseDateObj.setHours(0, 0, 0, 0);
        powerDatetimeObj.setHours(0, 0, 0, 0);
    
        return powerDatetimeObj >= purchaseDateObj;
    }

    validateRecordDatetime(purchaseDate, powerDatetime, recordedDatetime) {

        const powerDatetimeObj = new Date(powerDatetime);
        const recordedDatetimeObj = new Date(recordedDatetime);
    
        return recordedDatetimeObj >= powerDatetimeObj;
    }

    validateAssessmentForm() {
        let valid = [...this.template.querySelectorAll('lightning-input-field[data-groupname="assessmentFields"]')]
            .reduce((validSoFar, inputField) => {
                inputField.reportValidity();  
                return validSoFar && inputField.reportValidity();  
            }, true);
        return valid;
    }

    validateReturnPalletForm() {
        let valid = [...this.template.querySelectorAll('lightning-input-field[data-groupname="returnPalletFields"]')]
            .reduce((validSoFar, inputField) => {
                inputField.reportValidity();  
                return validSoFar && inputField.reportValidity();  
            }, true);
        return valid;
    }

    showError(message) {
        const evt = new ShowToastEvent({
            title: 'Error',
            message: message,
            variant: 'error',
        });
        this.dispatchEvent(evt);
    }
    
    // handleRowAction(event) {
    //     let dataLocal = JSON.parse(JSON.stringify(this.data));
    //     const actionName = event.detail.action.name;
    //     const row = event.detail.row;
    //     if (actionName === 'delete') {
    //         dataLocal.assessmentTableData = dataLocal.assessmentTableData.filter(record => record.uuid !== row.uuid);
    //         if(this.currentAssessmentForm.uuid == row.uuid){
    //             this.currentAssessmentForm = {};
    //         }
    //         let emptyData = true;
    //         for(let temp of dataLocal.assessmentTableData){
    //             if(temp.Assessment_Outcome__c != 'Draft'){
    //                 emptyData = false;
    //             }
    //         }
    //         if(emptyData){
    //             dataLocal.assessmentFaultCategory = null;
    //             dataLocal.assessmentFaultCategoryLable = null;
    //             dataLocal.assessmentFaultDescription = null;
    //             dataLocal.assessmentOutcome = null;
    //             dataLocal.assessmentStaffName = null;
    //             dataLocal.claimProductFinalOutcome = null;
    //             dataLocal.claimProductItemLocation = null;
    //             dataLocal.Return_Pallet__c = null;
    //             dataLocal.Return_Pallet_Name = null;
    //             dataLocal.addBackToStockActionLocation = null;
    //             dataLocal.selectProds = null;
    //             dataLocal.addBackToStockActionLocation = null;
                
    //         }
    //     } else if(actionName === 'print'){
    //         const rowData = dataLocal.assessmentTableData.find(record => record.uuid === row.uuid);
    //         if(this.isProductCategoryRefrigeration) {
    //             const childUrl = `/apex/claimFormFridgeAssessment?data=${encodeURIComponent(JSON.stringify(rowData))}`;
    //             window.open(childUrl, '_blank');
    //         } else if(this.isProductCategoryBushPower){
    //             const childUrl = `/apex/BushPowerAssessmentForm?data=${encodeURIComponent(JSON.stringify(rowData))}`;
    //             window.open(childUrl, '_blank');
    //         }

    //     } else if(actionName == 'edit'){ 
    //         this.currentAssessmentForm =  dataLocal.assessmentTableData.find(record => record.uuid === row.uuid);
    //         this.openAssessmentFormModal();
    //     }

    //     this.data = JSON.parse(JSON.stringify(dataLocal));
    //     this.updateDataObjToParent();
    // }

    updateDataObjToParent() {
        this.dispatchEvent(new CustomEvent('datachange', {
            detail: {
                data: this.data
            }
        }));
    }

    removeFileFromChild(event){
        this.data.uploadedFiles = event.detail.value.files;
        this.updateDataObjToParent();
    }

    sendEventToParent(eventname, eventObj) {
        console.log('eventname -> ' + JSON.stringify(eventname, null, 2));
        this.dispatchEvent(new CustomEvent(eventname, eventObj));
    }

    

    // ATTACHMENT RELATED STUFF
     /*
     * Parameters
        * event - onupdateddata event from the claimFormFileUpload component.
     * Merging of uploaded files to the Card Details.
    */
    linkFilesToCard(event) {
       this.sendEventToParent('linkfilestocard', event);
    }


    /*
     * Parameters
        * event - onremove event from the lightning pill component.
     * Deletion of the selected file.
    */
    removeFile(event) {
        let customEventVal = JSON.parse(JSON.stringify(event));
        customEventVal.detail.id = JSON.parse(JSON.stringify(event.target.dataset.id));
        this.sendEventToParent('removefile', customEventVal);
    }

    /*
     * Parameters
        * event - onremove event from the lightning pill component.
     * Deletion of the selected file.
    */
    removeFileForProductIssue(event) {
        console.log('in cp item remove File');
        console.log('event - ', JSON.stringify(event.detail));
        let fileID = event.detail.id;

        this.deleteFile(fileID, null, true);
        this.updateDataObjToParent();
    }

    /*
     * Parameters
        * fileID - Content Version ID of the Uploaded File.
     * Calling of deleteUploadedFiles Apex Method for the deletion of the uploaded file.
    */
    deleteFile(fileID, cardItemID, formatCard) {

        let cvIDList = [];

        if (formatCard) {

            this.isLoading = true;
            cvIDList.push(fileID);

        } else {

            this.modalLoading = true;
            this.disableModalButtons = {
                'confirm': true,
                'cancel': true
            };
            cvIDList = this.data.uploadedFiles;

        }

        deleteUploadedFiles({ cvIDList: cvIDList })
        .then((result) => {

            if (formatCard) {

                // let cardData = JSON.parse(JSON.stringify(this.data));
                let cardItem = JSON.parse(JSON.stringify(this.data.uploadedFiles));

                for (var i = 0; i < cardItem.length; i++) {
                    if (cardItem[i].fileID === fileID) {
                        cardItem.splice(i, 1);
                    }
                }

                // if ((!this.isCamper && cardItem.length == 0) || !this.validateSerialBatchValues(this.data) || !this.data.selectedResolution) {
                //     this.data.isValid = false;
                // } else {
                //     this.data.isValid = true;
                // }

                // this.cardData = cardData;
                // this.data = this.data[this.currentItemIndex];
                let dataLocal = JSON.parse(JSON.stringify(this.data));
                dataLocal.uploadedFiles = cardItem;
                this.data = {};
                this.data = JSON.parse(JSON.stringify(dataLocal));
                this.updateDataObjToParent();
                this.isLoading = false;

            } else {

                let pageName = 'orderProducts';
                let erpData = this.erpData;

                this.modalLoading = false;
                this.disableModalButtons = {
                    'confirm': false,
                    'cancel': false
                };
                this.data.uploadedFiles = [];
                this.sendNavigateEvent(pageName, erpData, null);

            }

        })
        .catch((error) => {
            let formattedError = ldsUtils.reduceErrors(error);
            // this.showError(formattedError);
        });

    }

    /*
     * Parameters
        * event - onclick event from the lightning-button-icon component.
     * Navigate to the Knowledge Article Community Page.
    */
    navigateURL(event) {
        window.open(this.serialBatchUrl, "_blank");
    }

    get hasAssessmentTableData() {
        return this.data.assessmentTableData != undefined && this.data.assessmentTableData.length > 0;
    }

    get isProductCategoryBushPower() {
        console.log('this.currentProductCategory-',this.currentProductCategory);
       this.isbatteryType = true;
        return this.currentProductCategory != undefined && this.currentProductCategory == 'Bush Power';
    }
    get isProductCategoryRefrigeration() {
        this.isRefrigerationType = true;
        return this.currentProductCategory != undefined && this.currentProductCategory == 'Refrigeration';
    }

    get fileIds() {
        if (this.data && this.data.uploadedFiles) {
            return this.data.uploadedFiles.filter(file => file.customValue != 'Photo with multimeter').map(file => file.fileID);
        }
        return [];
    }

    get isSerialRequired(){
        return this.data.didCauseDamage === 'No' && this.data.serialRequired;    
    }
    get isBatchRequired(){
        return this.data.didCauseDamage === 'No' && this.data.batchRequired;    
        // return false;
    }

    get isDisposalPhotoRequired() {
        return this.userProfileName == 'Warehouse';
    }

    /*get showCPFinalOutcome() {
        if(this.userProfileName == 'Store User' || this.userProfileName == 'Store Manager') {
            let failedOutcomes = ['Faulty', 'Goodwill'];
            if(this.data.assessmentOutcome != undefined && this.data.assessmentOutcome != '' && failedOutcomes.includes(this.data.assessmentOutcome) ) {
                return false;
            }
            
        } 

        return true;
    }*/

        get showCPFinalOutcome() {
          
            if (this.userProfileName && typeof this.userProfileName === 'string') {
                // if (this.userProfileName.includes('Store') || this.userProfileName.includes('Claim')) {
                //     let failedOutcomes = ['Faulty', 'Goodwill'];
                //     if (this.data.assessmentOutcome !== undefined && this.data.assessmentOutcome !== '' && failedOutcomes.includes(this.data.assessmentOutcome)) {
                //         return false;
                //     }
                // } else if (this.userProfileName === 'System Administrator' || this.userProfileName === 'Warehouse') {
                //     return true;
                // }
                    let failedOutcomes = ['Faulty', 'Goodwill','No Fault Found'];
                if ( (this.userProfileName === 'Warehouse' || this.userProfileName === 'System Administrator') && failedOutcomes.includes(this.data.assessmentOutcome)) {
                    return true;
                }
            }

        
            if(this.isNewAssessmentFormVisible == false) {
                return true;
            }
            return false; 
        }
        


}