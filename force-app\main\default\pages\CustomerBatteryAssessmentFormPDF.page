<!--
  @description       : 
  <AUTHOR> Aryan C
  @group             : 
  @last modified on  : 07-28-2025
  @last modified by  : Aryan C
-->
<apex:page standardController="Assessment_Form__c" extensions="AssessmentFormPDFController" renderAs="pdf" applyHtmlTag="false" showHeader="false" sidebar="false" standardStylesheets="false">
<head>
    <style type="text/css">
        @page {
            size: A4; /* or letter */
            margin: 0.75in; /* From your current page */
        }
        /* Base body styles from your current page */
        body { 
            font-family: Arial, sans-serif;
            font-size: 10pt; /* From your current page */
            color: #000000;
        }
        
        .pageHeaderContainer {
            display: table; 
            width: 100%;
            padding-bottom: 8px;       
            margin-bottom: 15px;       
        }

        .pageHeaderLogoCell {
            display: table-cell;
            width: auto; /* Adjust to logo size or set a fixed width e.g., 150px */
            padding-right: 15px; /* Space between logo and text */
            vertical-align: middle; /* Vertically align logo */
        }

        .pageHeaderTextCell {
            display: table-cell;
            text-align: center; /* Center the header text */
            vertical-align: middle; /* Vertically align text */
            font-size: 14pt;        /* Original font size */
            font-weight: bold;      /* Original font weight */
            font-family: Arial, sans-serif; /* Explicit font */
        }
        /* Section title style from your current page */
        .section-title { 
            font-size: 12pt; /* From your current page */
            font-weight: bold;
            background-color: #f0f0f0; /* From your current page */
            padding: 5px; /* From your current page */
            margin-top: 15px; /* From your current page */
            margin-bottom: 10px; /* From your current page */
            border: 1px solid #ddd; /* From your current page */
            font-family: Arial, sans-serif; /* Explicit font */
        }

        /* Main data table styling (from previous successful examples) */
        .dataTable {
            width: 100%; 
            border-collapse: collapse;
            margin-bottom: 10px; /* Space between tables, consistent with section-title margin */
            font-family: Arial, sans-serif; /* Explicit font */
            font-size: 10pt; /* Inherit from body or set explicitly */
            color: #000000;
        }
        .dataTable td {
            border: 1px solid #ddd; /* Match section-title border or use #b0b0b0 from other forms */
            padding: 6px 8px; 
            vertical-align: top;
            width: 50%; /* Each cell takes half the width by default */
        }
        .dataTable td.fullWidth { /* For cells that span both columns */
            width: 100%;
        }

        .fieldLabel { 
            font-weight: bold; 
            display: block; 
            margin-bottom: 4px; 
        }
        .fieldValue { 
            margin-bottom: 8px; 
            display: block;
            white-space: pre-wrap; /* For any multi-line values */
        }
        
        /* Checkbox rendering (kept for potential future use, though not used in this specific field list) */
        .checkboxDisplay { 
            display: inline-block; 
            width: 16px; 
            height: 16px;
            border: 1px solid #b0b0b0; 
            vertical-align: middle;
            margin-top: 2px; 
        }
        .checkboxChecked { 
            background-color: #707070; 
        }
        .checkboxUnchecked { 
            background-color: #FFFFFF;
        }

        /* For large text areas like "Notes" */
        .notesArea {
            min-height: 50px; /* Default min-height */
            /* border: 1px solid #ddd; /* Optional: border for the notes div itself */
            padding: 5px; /* Padding inside the notes area */
            white-space: pre-wrap; 
            margin-top: 2px; /* Space from label */
            display: block; 
        }
         .notesArea.large { /* For very large text areas if needed */
            min-height: 70px;
        }

        /* System Info specific font size (if a System Info section were added) */
        .systemInfoTable td,
        .systemInfoTable .fieldLabel,
        .systemInfoTable .fieldValue {
            font-size: 8pt; /* Example smaller font */
        }
    </style>
</head>
<body>
    <div class="pageHeaderContainer">
        <div class="pageHeaderLogoCell">
            <apex:image url="{!URLFOR($Resource.KingsLogoPNG)}"/>
        </div>
    </div>
    <div class="pageHeaderContainer">
        <div class="pageHeaderTextCell">
            Assessment Form: <apex:outputText value="{!Assessment_Form__c.Name}"/>
        </div>
    </div>

    <apex:form >
        <p>Customer Name: &nbsp;<apex:outputField value="{!Assessment_Form__c.Customer_Name__c}"/></p>
        <table class="dataTable">
            <tr>
                <td>
                    <span class="fieldLabel">Product</span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.Claim_Product__r.Product_Name__c}"/></span>
                </td>   
                <td>
                    <span class="fieldLabel">SKU</span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.Claim_Product__r.SKU__c}"/></span>
                </td>
            </tr>
             <tr>
                <td>
                    <span class="fieldLabel">Customer - Return Reason</span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.Claim_Product__r.Assessment_Fault_Category_value__c}"/></span>
                </td>   
                <td >
                    <span class="fieldLabel">Customer - Return Reason Description</span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.Claim_Product__r.Assesment_Fault_Description__c}"/></span>
                </td>
            </tr>
            <tr>
                <td>
                    <span class="fieldLabel">What is the voltage of the battery on arrival? </span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.Voltage_on_Arrival__c}"/></span>
                </td>
                 <td>
                    <span class="fieldLabel">Did the product charge correctly?</span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.Did_the_product_charge_correctly__c}"/></span>
                </td>
            </tr>
            <tr>
                <td>
                    <span class="fieldLabel">AC/DC Charge Finish Time/Date:</span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.AC_DC_Charge_Finish_Time_Date__c}"/></span>
                </td>
                <td>
                    <span class="fieldLabel">Voltage after charging</span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.Voltage_after_charging__c}"/></span>
                </td>      
            </tr>
            <tr>
                <td>
                    <span class="fieldLabel">Tested Capacity (AH)</span>
                    <span class="fieldValue"><apex:outputField value="{!Assessment_Form__c.Tested_Capacity_AH__c}"/></span>
                </td>
                 <td>
                    <span class="fieldLabel">Store Location</span>
                    <span class="fieldValue">{!usrLocation}</span>
                </td>
            </tr>
            <apex:outputPanel rendered="{!isPassed}">
                <tr>
                    <td colspan="2">
                        <span class="slds-box slds-theme_shade">
                            Your battery has passed testing and has been recharged.
                        </span>
                    </td>
                </tr>
            </apex:outputPanel>

            <apex:outputPanel rendered="{!NOT(isPassed)}">
                <tr>
                    <td colspan="2">
                        <span class="slds-box slds-theme_shade">
                            This Battery has failed testing.
                        </span>
                    </td>
                </tr>
            </apex:outputPanel>

        </table>
    </apex:form>

</body>
</apex:page>